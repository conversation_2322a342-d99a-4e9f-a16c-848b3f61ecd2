import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:four_leaf_poker/hive/street_action.dart';
import 'package:four_leaf_poker/menu_bar.dart';
import 'package:four_leaf_poker/models.dart';
import 'table_painter.dart'; // Same custom painter as in your main code

/// Offsets to apply on mobile: first = circle, second = tab
class SeatOffset {
  final Offset circle;
  final Offset tab;
  const SeatOffset(this.circle, this.tab);
}

class LandingPage extends StatelessWidget {
  LandingPage({Key? key}) : super(key: key);

  final int seatCount = 9;

  /// Same mobile offset overrides as main page:
  final Map<int, SeatOffset> _mobileSeatOffsetOverrides = {
    0: const SeatOffset(
      Offset(-5, 40), // circle offset
      Offset(-25, -50), // tab offset (unused here)
    ),
    1: const SeatOffset(
      Offset(-40, 0), // circle offset
      Offset(-25, -50), // tab offset (unused here)
    ),
    2: const SeatOffset(
      Offset(0, -20),
      Offset(5, -40),
    ),
    3: const SeatOffset(
      Offset(0, -30),
      Offset(-10, -40),
    ),
    4: const SeatOffset(
      Offset(-10, -40),
      Offset(-10, -30),
    ),
    5: const SeatOffset(
      Offset(10, -40),
      Offset(10, -30),
    ),
    6: const SeatOffset(
      Offset(0, -30),
      Offset(0, -40),
    ),
    7: const SeatOffset(
      Offset(0, -20),
      Offset(-15, -40),
    ),
    8: const SeatOffset(
      Offset(40, 0),
      Offset(15, -50),
    ),
  };

  double _seatCircleRx(BuildContext context) {
    final w = MediaQuery.of(context).size.width;
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (w * 0.85) / 2;
    }
    return 370.0;
  }

  double _seatCircleRy(BuildContext context) {
    final h = MediaQuery.of(context).size.height;
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (h * 0.30);
    }
    return 270.0;
  }

  List<Widget> _buildSeats(BoxConstraints constraints, BuildContext context) {
    final w = constraints.maxWidth;
    final h = constraints.maxHeight;
    final cx = w / 2;
    final cy = h / 2;

    final rx = _seatCircleRx(context);
    final ry = _seatCircleRy(context);

    final seats = <Widget>[];
    for (int i = 0; i < seatCount; i++) {
      final angle = (math.pi / 2) + i * (2 * math.pi / seatCount);
      var sx = cx + rx * math.cos(angle);
      var sy = cy + ry * math.sin(angle);

      final isMobile = MediaQuery.of(context).size.width < 600;
      if (isMobile && _mobileSeatOffsetOverrides.containsKey(i)) {
        final offsets = _mobileSeatOffsetOverrides[i]!;
        sx += offsets.circle.dx;
        sy += offsets.circle.dy;
      }

      seats.add(
        Positioned(
          left: sx - 22,
          top: sy - 22,
          child: Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color.fromARGB(255, 67, 68, 68),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  offset: const Offset(0, 3),
                  blurRadius: 6,
                ),
              ],
            ),
          ),
        ),
      );
    }
    return seats;
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      backgroundColor: Colors.grey.shade200,
      body: SizedBox.expand(
    child: 
         Stack(
        
        children: [

          // Dark background
          Positioned.fill(
            child: Container(

              decoration: const BoxDecoration(
                color: Color.fromARGB(255, 7, 32, 30),
                image: DecorationImage(
                    image: AssetImage('assets/images/bg.png'),
                    fit: BoxFit.cover,
                    opacity: .2),
              ),
            ),
          ),

          
          Positioned.fill(
            child: Center(
              
              child: Image.asset(
                'assets/images/flag1.png',
                width: 1300 * MediaQuery.of(context).size.width / 1920,
                fit: BoxFit.contain,
              ),
            )
            ),
          // Centered table with seats
         MyMenuBar(
            onRegister: () {},
            onLogin: () {},
          ),
],
      ),)
    );
  }

  List<Widget> _buildPlayerSeats({
    required double parentWidth,
    required double parentHeight,
    required bool isMobile,
    final empty = true,
  }) {
    List<Player> players = [];

    bool _userActionLock = false;

    List<bool> seatActive = [];
    int _selectedDealer =
        1; // 1-based seat number in UI, though we store 0-based internally
    bool alreadyDealt = false;
    // late List<bool> _isFolded;
    // late List<bool> _isAllIn;

    int _smallBlind = 100;
    int _bigBlind = 200;
    bool _isGameStarted = false;
    int? _hoveredSeatIndex;

    int? _myServerSeatIndex;

    int serverToLocal(int serverSeatIndex) {
      final seatCount = players.length;
      final mySeat = _myServerSeatIndex ?? 0;
      return (serverSeatIndex - mySeat + seatCount) % seatCount;
    }

    bool _hasShownGameOverModal = false;
    List<StreetAction> _streetActions = [];
    Map<int, String> _finalDescriptions = {};
    Map<int, List<String>> _showdownMap = {};

    /// Offsets to apply when on mobile, *keyed* by localIndex (0 = you)
    final Map<int, SeatOffset> _mobileSeatPositionOffsets = {
      //0: const SeatOffset( Offset(-7.5,  40), Offset(-25, -50) ),
      1: const SeatOffset(Offset(-40, 0), Offset(-25, -50)),
      2: const SeatOffset(Offset(0, -20), Offset(5, -40)),
      3: const SeatOffset(Offset(0, -30), Offset(-10, -40)),
      4: const SeatOffset(Offset(-10, -40), Offset(-10, -30)),
      5: const SeatOffset(Offset(10, -40), Offset(10, -30)),
      6: const SeatOffset(Offset(0, -30), Offset(0, -40)),
      7: const SeatOffset(Offset(0, -20), Offset(-15, -40)),
      8: const SeatOffset(Offset(40, 0), Offset(15, -50)),
    };

    final seatCount = players.length;
    final mySeat = _myServerSeatIndex ?? 0;

    // Example "roomId" from server. In a real app, you'd store or fetch it dynamically.
    late final String _roomId;

    bool _didJoinAlready = false;
    bool _didLoadArgs = false;

    final seatWidgets = <Widget>[];

    // Center of the table
    final cx = parentWidth / 2;
    final cy = parentHeight / 2;

    // Radii for ellipse
    final rx = _seatCircleRx;
    final ry = _seatCircleRy;

    for (int offset = 0; offset < seatCount; offset++) {
      // "Real" seat index rotated so mySeat is at offset=0
      final realSeat = (mySeat + offset) % seatCount;

      // Compute angle and base position
      final angle = (math.pi / 2) + offset * (2 * math.pi / seatCount);
      // final baseX = cx + rx * math.cos(angle);
      // final baseY = cy + ry * math.sin(angle);

      // // Initialize both circle and tab positions to base
      // var sxCircle = baseX;
      // var syCircle = baseY;
      // var sxTab = baseX;
      // var syTab = baseY;

      // Apply mobile-specific overrides if provided
      // if (isMobile && _mobileSeatPositionOffsets.containsKey(offset)) {
      //   final o = _mobileSeatPositionOffsets[offset]!;
      //   sxCircle += o.circle.dx;
      //   syCircle += o.circle.dy;
      //   sxTab    += o.tab.dx;
      //   syTab    += o.tab.dy;
      // }

      print('realSeat: $realSeat');
      // Inactive seat: draw the seat circle with number
      seatWidgets.add(
        Positioned(
          // left: sxCircle - 100,
          // top:  syCircle  - 65,
          child: Transform.rotate(
              angle: ![4, 5].contains(realSeat) ? angle + math.pi / 2 : 0,
              //angle:0,
              child:
                  //Text(angle.toString()),
                  Image.asset(
                'assets/images/chair.png',
                width: 200,
                height: 154,
                fit: BoxFit.scaleDown,
              )),
        ),
      );
    }

    return seatWidgets;
  }
}
