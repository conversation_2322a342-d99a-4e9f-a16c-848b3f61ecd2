import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // for Clipboard
import 'package:four_leaf_poker/menu_bar.dart';
import 'package:four_leaf_poker/table/extras/action_log_modal.dart';
import 'package:four_leaf_poker/table/extras/card_painter.dart';
import 'package:four_leaf_poker/table/extras/winnings_display.dart';
import 'package:four_leaf_poker/table/header/custom_menu.dart';
import 'package:four_leaf_poker/table/player.dart';
import 'package:four_leaf_poker/table/villian.dart';
import 'package:hive/hive.dart';
import 'package:http/http.dart' as http;
import 'package:four_leaf_poker/api.dart';
import 'package:four_leaf_poker/hive/hand_record.dart';
import 'package:four_leaf_poker/hive/player_stats.dart';
import 'package:four_leaf_poker/hive/street_action.dart';
import 'package:stacked/stacked_annotations.dart';
import 'package:universal_html/html.dart' as html;
import 'ai_brain.dart'; // AI logic
import 'models.dart'; // Our data models
import 'table_painter.dart'; // The custom painter
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'action_history.dart'; // The collapsible history panel
import 'package:four_leaf_poker/config_service.dart';

/// The main PokerTablePage
class PokerTablePage extends StatefulWidget {
  final String roomId;

  // Annotate the constructor param so Stacked will populate :roomId
  const PokerTablePage({
    Key? key,
    @PathParam('roomId') required this.roomId,
  }) : super(key: key);

  @override
  State<PokerTablePage> createState() => _PokerTablePageState();
}

class _ChopPayoutItem {
  final int seatIndex;
  final String label;
  final Offset start;
  final Offset end;

  _ChopPayoutItem({
    required this.seatIndex,
    required this.label,
    required this.start,
    required this.end,
  });
}

class PotLabel extends StatefulWidget {
  final int potIndex;
  final String label;
  final bool isSmall;
  final Function(int potIndex, Offset globalPos) onMeasured;

  const PotLabel({
    Key? key,
    required this.potIndex,
    required this.label,
    required this.onMeasured,
    this.isSmall = false,
  }) : super(key: key);

  @override
  State<PotLabel> createState() => _PotLabelState();
}

class _PotLabelState extends State<PotLabel> {
  @override
  void initState() {
    super.initState();
    // We can't measure right away because the widget isn't laid out yet.
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Delaying the measurement to after layout
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureAndReport();
    });
  }

  void _measureAndReport() {
    final renderObj = context.findRenderObject();
    if (renderObj is RenderBox) {
      final offset = renderObj.localToGlobal(Offset.zero);
      widget.onMeasured(widget.potIndex, offset);
    }
  }

  @override
  Widget build(BuildContext context) {
    final double fs = 12.0;

    return Container(
      // bump the vertical padding so it’s taller
      width: 150,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
        border: Border.all(color: Colors.black, width: 1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Stack(
                                                            alignment: Alignment
                                                                .center,
                                                            children: [
                                                              Text(
                                                                widget.label,
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  foreground:
                                                                      Paint()
                                                                        ..style =
                                                                            PaintingStyle.stroke
                                                                        ..strokeWidth =
                                                                            4
                                                                        ..color =
                                                                            Colors.black,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              ),
                                                              Text(
                                                                widget.label,
                                                                style:
                                                                    const TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              )
                                                            ]),
                                                ]));
  }
}

class _PokerTablePageState extends State<PokerTablePage>
    with TickerProviderStateMixin {
  // ================
  // PLAYERS + SEATS
  // ================
  List<Player> players = [];

  bool _userActionLock = false;

  List<bool> seatActive = [];
  int _selectedDealer =
      1; // 1-based seat number in UI, though we store 0-based internally
  bool alreadyDealt = false;
  // late List<bool> _isFolded;
  // late List<bool> _isAllIn;

  int _smallBlind = 100;
  int _bigBlind = 200;
  bool _isGameStarted = false;
  int? _hoveredSeatIndex;

  List<int> _handStartingStacks = List.filled(9, 0);

  String? _generatedHandId;

  int? _myServerSeatIndex;

  int serverToLocal(int serverSeatIndex) {
    final seatCount = players.length;
    final mySeat = _myServerSeatIndex ?? 0;
    return (serverSeatIndex - mySeat + seatCount) % seatCount;
  }

  bool _hasShownGameOverModal = false;
  List<StreetAction> _streetActions = [];
  Map<int, String> _finalDescriptions = {};
  Map<int, List<String>> _showdownMap = {};

  // Example "roomId" from server. In a real app, you'd store or fetch it dynamically.
  late final String _roomId;

  bool _didJoinAlready = false;
  bool _didLoadArgs = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_didLoadArgs) {
      _didLoadArgs = true;

      // Attempt to read from route arguments first
      final args = ModalRoute.of(context)?.settings.arguments;

      final storedTableId = html.window.localStorage['tableId'];
      final storedPlayerId = html.window.localStorage['playerId'];

      //final args = ModalRoute.of(context)?.settings.arguments;
      if (args is Map<String, dynamic>) {
        final argTableId = args['tableId'] as String?;
        final argPlayerId = args['playerId'] as String?;
        // Optional: final argName = args['playerName'] as String?;

        // If localStorage is null/empty/mismatched, fill it from the arguments
        if (storedTableId == null ||
            storedTableId.isEmpty ||
            storedTableId != argTableId) {
          html.window.localStorage['tableId'] = argTableId ?? '';
        }
        if (storedPlayerId == null ||
            storedPlayerId.isEmpty ||
            storedPlayerId != argPlayerId) {
          html.window.localStorage['playerId'] = argPlayerId ?? '';
        }
      }

      final finalTableId = html.window.localStorage['tableId'];
      final finalPlayerId = html.window.localStorage['playerId'];

      if (finalTableId != null &&
          finalPlayerId != null &&
          finalTableId == widget.roomId) {
        // if (socket.connected) {
        //   socket.emit("playerJoinedTable", {
        //     "roomId": widget.roomId,
        //     "playerId": html.window.localStorage['playerId'],
        //   });
        // } else {
        //   socket.connect(); // will trigger onConnect → join correctly
        // }
      }

      if (args is Map<String, dynamic>) {
        final passedName = args['playerName'] as String?;
        final seatPosStr = args['seatPosition']?.toString() ?? '1';
        final seatIndex = int.tryParse(seatPosStr) ?? 1;
        final seat0Based = seatIndex - 1;

        // Store seat in localStorage (optional, in case we need it later)
        html.window.localStorage['seatPosition'] = seat0Based.toString();
        // Also store playerName so we can pass it to joinTable
        if (passedName != null) {
          html.window.localStorage['playerName'] = passedName;
        }

        // If we have a valid seat and name, set them in our local arrays
        if (passedName != null &&
            seat0Based >= 0 &&
            seat0Based < players.length) {
          players[seat0Based].name = passedName;
          seatActive[seat0Based] = true;
          _playerControls[seat0Based] = true;
        }

        _myServerSeatIndex = seat0Based;
      }

      // If we still have no seat, read from local storage
      if (_myServerSeatIndex == null) {
        final storeIndexStr = html.window.localStorage['seatPosition'];
        if (storeIndexStr != null) {
          final seat = int.tryParse(storeIndexStr);
          if (seat != null && seat >= 0 && seat < players.length) {
            _myServerSeatIndex = seat;
          }
        }
        // If STILL null, then fallback:
        if (_myServerSeatIndex == null) {
          _myServerSeatIndex = 0;
        }
      }
      _playerControls[_myServerSeatIndex!] = true;

      //html.window.alert("Browser seat index => $_myServerSeatIndex");

      // Whichever seat we end up with, ensure it’s user-controlled
      if (_myServerSeatIndex! >= 0 && _myServerSeatIndex! < players.length) {
        seatActive[_myServerSeatIndex!] = true;
        _playerControls[_myServerSeatIndex!] = true;
      }
    }
  }

  // Helper to see if any seat is still open.
  bool _anySeatOpen() {
    // If any seat is false => it's open
    return seatActive.contains(false);
  }

  // Count how many seats are active
  int _countActiveSeats() {
    return seatActive.where((isActive) => isActive).length;
  }

  // Button handler: Start the game (needs >=2 players)
  void _onStartGamePressed() {
    print('Active seats: ${seatActive.where((x) => x).length}');
    if (_countActiveSeats() < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text("Need at least 2 players to start the game!")),
      );
      return;
    }

    // Instead of doing all the deck building locally, do:
    socket.emit("startGame", {
      "roomId": _roomId,
    });
  }

  /// how wide our seat‑circle should be
  double get _seatCircleRx {
    final w = MediaQuery.of(context).size.width;
    // if portrait (e.g. 390×844), make it ~90% of width/2
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (w * 0.85) / 2;
    }
    // otherwise fall back to your existing value
    return 370.0;
  }

  /// how tall our seat‑circle should be
  double get _seatCircleRy {
    final h = MediaQuery.of(context).size.height;
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (h * 0.3); // tweak this multiplier until it “feels” right
    }
    return 270.0;
  }

  /// Calculate scale factor based on screen width (1920px is reference)
  double get _screenScaleFactor {
    final screenWidth = MediaQuery.of(context).size.width;
    return 1;
  }

  /// Get scaled card dimensions for different contexts
  double get _scaledCardWidth => 60.0 ;
  double get _scaledCardHeight => 90.0;

  double get _scaledSmallCardWidth => 50.0 ;
  double get _scaledSmallCardHeight => 70.0 ;

  // ================
  // TABLE / DECK
  // ================
  late List<CardModel> deck;
  List<CardModel> communityCards =
      List.filled(5, CardModel('', ''), growable: false);
  List<CardModel> burnedCards = [
    CardModel('', ''),
    CardModel('', ''),
    CardModel('', '')
  ];

  GameStage gameStage = GameStage.preHand;

  // True/false for whether each seat is user-controlled or AI:
  // seat 0 is user-controlled by default
  List<bool> _playerControls = List.filled(9, false);

  int _actualPot = 0;
  int _displayedPot = 0;
  int get pot => _displayedPot;

  bool showAllCards = false;

  int? _dealerIndex;
  int? _smallBlindIndex;
  int? _bigBlindIndex;

  bool _showCountdown = false;
  String _countdownText = "";
  Timer? _countdownTimer;
  final List<(int seatIndex, CardModel card)> _cardsToDeal = [];
  // ================
  // BETTING STATE
  // ================
  List<String> _playerActions = List.filled(9, "...");
  List<int> _amountsOnTable = List.filled(9, 0);
  int _currentBet = 0;
  int? _currentTurnIndex;
  int? _lastRaiserIndex;
  int? _roundFirstActorIndex;
  bool _bigBlindActedPreflop = false;
  bool _bettingRoundInProgress = false;
  final Set<int> _seatsToAct = {};

  Map<String, dynamic>? _pendingTurnStartData;

  //bool _hasShownEliminationModal = false;
  bool _isEliminated = false; // (Optional convenience flag)

  // ================
  // DEAL ANIMATION
  // ================
  late AnimationController _dealController;
  late Animation<Offset> _dealAnimation;
  bool _isDealing = false;
  int _dealingIndex = 0;
  Offset _flyingCardOffset = Offset.zero;

  // ================
  // ODDS STORAGE
  // ================
  List<dynamic> _singleResults = [];
  List<List<dynamic>> _allResults = [];
  bool _lastWasAll = false;
  bool _isRunout = false;

  int? _winnerIndex;
  List<SidePot> _sidePots = [];

  final double _mainPlayerExtraYOffset = 40;
  bool _hasAutoFoldedThisTurn = false;
  late final AnimationController _turnTimerController;
  static const double _turnRingWidth = 5.5;
  // ================
  // SLIDER FIELDS
  // ================
  bool _showSlider = false;
  bool _sliderIsRaise = false;
  double _sliderValue = 0;
  double _sliderMin = 0;
  double _sliderMax = 0;

  final ScrollController _scrollCtrl = ScrollController();
  late int _serverTurnDurationMs = 20000;
  // ======================================
  // HAND HISTORY (ACTION HISTORY) FIELDS
  // ======================================
  List<String> _handHistory = [];
  bool _historyExpanded = false;
  final ScrollController _historyScrollController = ScrollController();
  HistoryPanelState _historyPanelState = HistoryPanelState.closed;

  bool _isLoading = false;
  // ================
  // PENDING BETS + CHIPS
  // ================
  List<int> _pendingBets = List.filled(9, 0);

  late AnimationController _chipCollectController;
  bool _isCollectingBets = false;
  List<Offset> _chipOffsets = List.filled(9, Offset.zero);

  // ================
  // PLAYER STATS
  // ================
  List<int> _seatsSawFlop = [];
  List<int> _seatsSawTurn = [];
  List<int> _seatsSawRiver = [];

  String? _lastStreet;
  // ================
  // PAYOUT ANIMATION
  // ================
  final Map<int, double> _seatPendingChips = {};
  final List<List<int>> _potWinnerSeats = [];
  int _payoutAnimatingPotIndex = 0;
  bool _isAnimatingPayout = false;

  late AnimationController _payoutController;
  double _payoutAnimationValue = 0.0;
  List<_ChopPayoutItem> _currentPotChopItems = [];
  Map<int, List<Map<String, dynamic>>> _builtPotChopShares = {};
  final Map<int, Offset> _potLabelOffsets = {};
  final List<GlobalKey<_PotLabelState>> _potLabelKeys = [];

  /// Offsets to apply when on mobile, *keyed* by localIndex (0 = you)
  final Map<int, SeatOffset> _mobileSeatPositionOffsets = {
    //0: const SeatOffset( Offset(-7.5,  40), Offset(-25, -50) ),
    1: const SeatOffset(Offset(-40, 0), Offset(-25, -50)),
    2: const SeatOffset(Offset(0, -20), Offset(5, -40)),
    3: const SeatOffset(Offset(0, -30), Offset(-10, -40)),
    4: const SeatOffset(Offset(-10, -40), Offset(-10, -30)),
    5: const SeatOffset(Offset(10, -40), Offset(10, -30)),
    6: const SeatOffset(Offset(0, -30), Offset(0, -40)),
    7: const SeatOffset(Offset(0, -20), Offset(-15, -40)),
    8: const SeatOffset(Offset(40, 0), Offset(15, -50)),
  };

  bool _isGameOver = false;

  late IO.Socket socket;
  bool _socketListenersSet = false;
  int numPlayers = 0;

  @override
  void initState() {
    super.initState();
    _roomId = widget.roomId;
    // 1) Create socket with autoConnect disabled
    socket = IO.io(
      ConfigService.baseUrl,
      IO.OptionBuilder()
          .setTransports(['websocket'])
          .disableAutoConnect()
          .build(),
    );

    socket.off('playerListUpdated');
    socket.off('joinedMidHand');
    socket.off('startHand');
    socket.off('turnStart');
    socket.off('actionUpdate');
    socket.off('streetUpdate');
    socket.off('collectChipsAnimation');
    socket.off('sidePotsUpdate');
    socket.off('showdownResults');
    socket.off('nextHandCountdown');
    socket.off('gameOver');
    socket.off('playersEliminated');
    socket.off('removedFromTable');
    socket.off('duplicateSession');
    socket.off('inactivityBoot');
    socket.off('knockedOut');
    // 2) On connect, read localStorage and join
    // socket.onConnect((_) {
    //   final tableId  = html.window.localStorage['tableId'];
    //   final playerId = html.window.localStorage['playerId'];
    //   if (tableId == widget.roomId && playerId != null) {
    //     socket.emit("playerJoinedTable", {
    //       "roomId":   tableId,
    //       "playerId": playerId,
    //     });
    //   }
    // });
    if (!_socketListenersSet) {
      _socketListenersSet = true;
      socket.on("playersEliminated", (data) {
        final eliminatedIds = (data["eliminatedIds"] as List).cast<String>();

        setState(() {
          for (int i = 0; i < players.length; i++) {
            if (eliminatedIds.contains(players[i].id)) {
              seatActive[i] = false;
            }
          }
        });
      });

      socket.on("sidePotsUpdate", (data) {
        final pots = (data['sidePots'] as List).map((p) {
          return SidePot(
            p['amount'] as int,
            (p['eligibles'] as List).cast<int>(),
          );
        }).toList();

        // 2) pull the new player stacks
        final updatedPlayers =
            (data['players'] as List).cast<Map<String, dynamic>>();

        setState(() {
          _sidePots = pots;
          // if you want the total displayed pot to still match the sum:
          _displayedPot = pots.fold(0, (sum, sp) => sum + sp.amount);
          for (final up in updatedPlayers) {
            final seat = up['seatNumber'] as int;
            final stk = up['stack'] as String;
            if (seat >= 0 && seat < players.length) {
              players[seat].chips = stk;
            }
          }
        });
      });

      // 4) Listen for "playerListUpdated" to refresh your local state
      socket.on("playerListUpdated", (data) {
        if (data is List) {
          setState(() {
            // Optionally reset first, so we rebuild from scratch:
            for (int i = 0; i < players.length; i++) {
              if (players[i] == null) {
                return;
              }
              ;
              players[i].name = '';
              players[i].chips = '0';
              seatActive[i] = false;
            }

            print(players[0]);
            print(players[1]);
            print(players[2]);
            print(players[3]);
            print(players[4]);
            print(players[5]);
            print(players[6]);
            print(players[7]);
            print(players[8]);
            print(data);

            for (final p in data) {
              final seatIndex = p['seatNumber'] as int;
              if (seatIndex >= 0 && seatIndex < players.length) {
                players[seatIndex].id = p['id'] as String;
                players[seatIndex].name = p['name'] ?? '';
                players[seatIndex].chips = p['stack'] ?? '0';
                seatActive[seatIndex] = true;
              }
            }
          });
        }
      });

      socket.on("removedFromTable", (_) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (ctx) {
            return Dialog(
              backgroundColor: const Color(0xFF2E2D2D),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
              child: Container(
                width: 340, // same small width
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF2E2D2D),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                        color: Colors.black26,
                        offset: Offset(0, 4),
                        blurRadius: 10),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      "You have been removed from the table.",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color.fromARGB(255, 4, 159, 206),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(ctx).pop();
                        Navigator.of(context).pushReplacementNamed("/");
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color.fromARGB(255, 4, 159, 206),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6)),
                      ),
                      child: const Text("OK",
                          style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      });

      // inside initState():
      socket.on("duplicateSession", (_) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (ctx) {
            return Dialog(
              backgroundColor: const Color(0xFF2E2D2D),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
              child: Container(
                width: 340,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF2E2D2D),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                        color: Colors.black26,
                        offset: Offset(0, 4),
                        blurRadius: 10),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      "Session Replaced",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color.fromARGB(255, 4, 159, 206),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      "You have joined the table on a new location.",
                      style: TextStyle(color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(ctx).pop();
                        Navigator.of(context).pushReplacementNamed("/");
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color.fromARGB(255, 4, 159, 206),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6)),
                      ),
                      child: const Text("OK",
                          style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      });

      socket.on("turnStart", (data) {
        // always save it
        _pendingTurnStartData = data as Map<String, dynamic>;

        if (!_isDealing) {
          _startTurnTimer(data);
          _pendingTurnStartData = null;
        }
      });

      socket.on("knockedOut", (_) {
        _showKnockedOutModal();
      });

      socket.on("gameOver", (data) {
        print('Received gameOver event');

        // First, get my player ID from localStorage
        final myPlayerId = html.window.localStorage['playerId'];
        final winnerId = data["winnerId"] as String?;

        // If we've already shown it, bail out
        if (_hasShownGameOverModal) {
          print('Already shown game over modal, ignoring');
          return;
        }

        // Eliminated players don't get the game over modal
        if (_isEliminated) {
          print('Player is eliminated, not showing game over');
          return;
        }

        // Stop all ongoing animations and timers
        _turnTimerController.stop();
        _chipCollectController.stop();
        _payoutController.stop();
        _dealController.stop();

        setState(() {
          _userActionLock = true;
          _isGameOver = true;
          _countdownText = "Game Over!";
          // Set this flag BEFORE showing the modal to prevent race conditions
          _hasShownGameOverModal = true;
        });

        // Only show winner-specific message to the winner
        final bool isWinner =
            (myPlayerId != null && winnerId != null && myPlayerId == winnerId);
        final winnerServerSeat = data["winnerSeat"] as int?;

        // Get winner name
        String winnerName = "???";
        if (winnerServerSeat != null &&
            winnerServerSeat >= 0 &&
            winnerServerSeat < players.length) {
          winnerName = players[winnerServerSeat].name;
        }

        // Show the appropriate modal
        _showGameOverModal(winnerName, isWinner);
      });

      socket.on("inactivityBoot", (_) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) => AlertDialog(
            title: Text("Removed for Inactivity"),
            content:
                Text("You have been removed from the table for inactivity."),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pushReplacementNamed("/");
                },
                child: Text("OK"),
              ),
            ],
          ),
        );
      });

      socket.on("nextHandCountdown", (data) {
        final secs = data["seconds"] ?? 5;
        setState(() {
          _showCountdown = true;
          if (secs > 0) {
            _countdownText = "$secs..";
          } else {
            // If seconds == 0, show "Next Hand!"
            _countdownText = "Next Hand!";
          }
        });
      });

      socket.on("showdownResults", (data) {
        print(">>> showdownResults: $data");

        setState(() {
          gameStage = GameStage.showdown;

          final stillIn = players.where((p) => !p.isFolded).length;
          if (stillIn > 1) {
            showAllCards = true;
          } else {
            // If only one seat remains, do NOT set showAllCards=true
            showAllCards = false;
          }

          // Clear old pot data
          _sidePots.clear();
          _builtPotChopShares.clear();

          // We'll track totalPot for display
          int totalPot = 0;

          final potRes = data["potResults"] as List?;
          if (potRes != null) {
            for (final pr in potRes) {
              final potIndex = pr["potIndex"] as int;
              final potAmount = pr["potAmount"] as int;
              final winners = (pr["winnerSeats"] as List).cast<int>();
              final chopSize = (pr["chopSize"] as num).toDouble();

              // Make sure our _sidePots list is big enough
              while (_sidePots.length <= potIndex) {
                _sidePots.add(SidePot(0, []));
              }
              _sidePots[potIndex] = SidePot(potAmount, winners);
              totalPot += potAmount;

              // Build pot chop info
              _builtPotChopShares[potIndex] ??= [];
              if (winners.length == 1) {
                // single winner
                _builtPotChopShares[potIndex]!.add({
                  "seat": winners.first,
                  "amount": potAmount.toDouble(),
                });
              } else {
                // multi-winner
                for (final w in winners) {
                  _builtPotChopShares[potIndex]!.add({
                    "seat": w,
                    "amount": chopSize,
                  });
                }
              }
            }
          }

          _displayedPot = totalPot;
          _actualPot = totalPot;

          // === NEW: CLEAR old winner-loser labels so we can re-set them
          for (int i = 0; i < players.length; i++) {
            // If seat was folded or never active, keep them as "Fold" or "..."
            // We'll overwrite only if they ended up in showdown.
            if (seatActive[i] && !players[i].isFolded) {
              // Temporarily set them to "Lost"
              // We can overwrite with "Win" or "Chop" if we find them below.
              _playerActions[i] = "Lost";
            }
          }

          // Next, build seatPotWins or seatPotChops from potRes
          final seatPotWins = <int, List<int>>{};
          final seatPotChops = <int, List<int>>{};
          for (int i = 0; i < players.length; i++) {
            seatPotWins[i] = [];
            seatPotChops[i] = [];
          }
          // For each pot i => read potResults[i] => fill seatPotWins or seatPotChops
          if (potRes != null) {
            for (final pr in potRes) {
              final potIndex = pr["potIndex"] as int;
              final potAmount = pr["potAmount"] as int;
              final winners = (pr["winnerSeats"] as List).cast<int>();

              if (winners.length == 1) {
                final w = winners.first;
                seatPotWins[w]!.add(potIndex + 1);
              } else if (winners.length > 1) {
                for (final w in winners) {
                  seatPotChops[w]!.add(potIndex + 1);
                }
              }
            }
          }

          // Mark each seat's action label: "Win: Pot 1,2" or "Chop: Pot 1,2" or "Lost"
          _winnerIndex =
              null; // We'll set this if exactly 1 seat truly "wins" (optional)
          for (int i = 0; i < players.length; i++) {
            if (!seatActive[i] || players[i].isFolded) {
              continue; // folded or not in showdown
            }
            final winsList = seatPotWins[i]!;
            final chopsList = seatPotChops[i]!;

            if (winsList.isEmpty && chopsList.isEmpty) {
              // already set "Lost" above
            } else {
              String winsStr =
                  winsList.isNotEmpty ? "Win: Pot ${winsList.join(',')}" : "";
              String chopStr = chopsList.isNotEmpty
                  ? "Chop: Pot ${chopsList.join(',')}"
                  : "";

              if (winsStr.isNotEmpty && chopStr.isNotEmpty) {
                _playerActions[i] = "$winsStr, $chopStr";
              } else if (winsStr.isNotEmpty) {
                _playerActions[i] = winsStr;
                // If you want a single winner highlight, set _winnerIndex if only one pot?
                _winnerIndex = i;
              } else {
                _playerActions[i] = chopStr;
              }
            }
          }

          final newHistory = data["handHistory"] as List<dynamic>?;
          if (newHistory != null && newHistory.isNotEmpty) {
            // Option A: On each new hand, clear the client’s old list
            _updateHandHistory(newHistory);
          }

          // == The usual final step: Start the payout animation
          gameStage = GameStage.payout;
          _userActionLock = true;
          _payoutAnimatingPotIndex = 0;
          _isAnimatingPayout = true;

          if (_sidePots.isEmpty) {
            // no side pots => skip straight to finalize
            _finalizePayouts();
          } else {
            _buildCurrentPotChopItems(0);
            _payoutController.forward();
          }
        });
      });

      socket.on("info", (msg) {
        print("[CLIENT] info => $msg");
      });

      socket.on("collectChipsAnimation", (data) async {
        await Future.delayed(const Duration(milliseconds: 500));

        setState(() {
          // 1) Set offsets for each seat:
          for (int i = 0; i < players.length; i++) {
            _chipOffsets[i] = _seatBetOffset(i);
          }

          // 2) Then animate
          _isCollectingBets = true;
          _chipCollectController.reset();
          _chipCollectController.forward();
        });
      });

      socket.on("joinedMidHand", (data) {
        print(">>> joinedMidHand data => $data");
        final turnIndex = data["currentTurnIndex"] as int?;
        final deadlineMs = data["turnDeadline"] as int? ?? 0;
        final durationMs = data["turnDurationMs"] as int? ?? 0;
        final now = DateTime.now().millisecondsSinceEpoch;

        setState(() {
          // 0) reset per-seat arrays
          final seatCount = players.length;
          seatActive = List.filled(seatCount, false);
          _amountsOnTable = List.filled(seatCount, 0);
          _playerActions = List.filled(seatCount, "...");
          _playerControls = List.filled(seatCount, false);
          _pendingBets = List.filled(seatCount, 0);

          // 1) extract table state
          final newPot = data["pot"] as int? ?? 0;
          final cBet = data["currentBet"] as int? ?? 0;
          final curStreet = data["currentStreet"] as String? ?? "preflop";
          final turnIndex = data["currentTurnIndex"] as int?;
          final burned = data["burnedCards"] as List? ?? [];
          final community = data["communityCards"] as List? ?? [];
          final foldsArr = data["isFolded"] as List? ?? [];
          final allInArr = data["isAllIn"] as List? ?? [];
          final amountsArr = data["amountsOnTable"] as List? ?? [];
          final seatDataArr = data["seatData"] as List? ?? [];
          final historyArr = data["handHistory"] as List? ?? [];

          // 2) core game state
          _isGameStarted = true;
          _displayedPot = newPot;
          _actualPot = newPot;
          _currentBet = cBet;
          _currentTurnIndex = turnIndex;
          _serverTurnDurationMs = durationMs;

          // 3) set the board / burn based on street
          switch (curStreet) {
            case "flop":
              gameStage = GameStage.flop;
              break;
            case "turn":
              gameStage = GameStage.turn;
              break;
            case "river":
              gameStage = GameStage.river;
              break;
            case "showdown":
              gameStage = GameStage.showdown;
              showAllCards = true;
              break;
            default:
              gameStage = GameStage.preHand;
          }

          // Burn cards
          for (int i = 0; i < 3; i++) {
            if (i < burned.length) {
              final bc = burned[i];
              burnedCards[i] = CardModel(bc["rank"], bc["suit"]);
            } else {
              burnedCards[i] = CardModel('', '');
            }
          }
          // Community cards
          for (int i = 0; i < 5; i++) {
            if (i < community.length) {
              final cc = community[i];
              communityCards[i] =
                  CardModel(cc["rank"], cc["suit"], faceUp: true);
            } else {
              communityCards[i] = CardModel('', '', faceUp: false);
            }
          }

          // 4) folded / all-in flags
          for (int i = 0; i < seatCount; i++) {
            players[i].isFolded = (i < foldsArr.length && foldsArr[i] == true);
            players[i].isAllIn = (i < allInArr.length && allInArr[i] == true);
          }

          // 5) how much each has put in this street
          for (final obj in amountsArr) {
            final s = obj["seat"] as int? ?? -1;
            final a = obj["amt"] as int? ?? 0;
            if (s >= 0 && s < seatCount) {
              _amountsOnTable[s] = a;
              players[s].contributedThisStreet = a;
            }
          }

          // 6) seat data + position tokens
          //    (Everything in ONE setState so we don't need a second pass)
          _dealerIndex = null;
          _smallBlindIndex = null;
          _bigBlindIndex = null;

          for (final sd in seatDataArr) {
            final s = sd["seatNumber"] as int? ?? -1;
            if (s < 0 || s >= seatCount) continue;

            // basic info
            players[s].name = sd["name"] as String? ?? "";
            players[s].chips = sd["stack"] as String? ?? "0";
            seatActive[s] = !(sd["eliminated"] as bool? ?? false);
            _playerControls[s] = (s == _myServerSeatIndex);

            // pick up D / SB / BB
            if (sd["dealer"] == true) _dealerIndex = s;
            if (sd["sb"] == true) _smallBlindIndex = s;
            if (sd["bb"] == true) _bigBlindIndex = s;

            // hole cards (only your own will have cards)
            final raw = sd["holeCards"] as List? ?? [];
            if (raw.length >= 2) {
              players[s].hand = raw.map((c) {
                return CardModel(c["rank"] as String, c["suit"] as String);
              }).toList();
            } else {
              players[s].hand = [];
            }
          }

          // 7) restart your turn timer
          _turnTimerController
            ..stop()
            ..reset();

          // 8) update history intelligently to avoid resetting ActionLogModal
          _updateHandHistory(historyArr);

          // 9) lock actions if it's not your turn
          _userActionLock = (_currentTurnIndex != _myServerSeatIndex);
        });

        if (turnIndex == _myServerSeatIndex && durationMs > 0) {
          // Compute how much time is left
          final remaining = (deadlineMs - now).clamp(0, durationMs);
          _turnTimerController
            ..stop()
            ..duration = Duration(milliseconds: remaining)
            ..reset()
            ..forward();
        }
      });

      socket.on("streetUpdate", (data) {
        // Example structure of data:
        // {
        //   "currentStreet": "flop" | "turn" | "river" | "showdown",
        //   "burnedCards": [...],
        //   "communityCards": [...],
        //   "pot": 600,
        //   "currentBet": 200,
        //   "currentTurnIndex": 2,
        //   "deck": [...],
        //   "isFolded": [...],
        //   "isAllIn": [...],
        //   "amountsOnTable": [...],
        //   "players": [
        //     {"seatNumber": 0, "stack": "1500", "name": "Alice", "dealer": false, "sb": true, ...},
        //     ...
        //   ],
        // }

        //debugPrint(">>> streetUpdate received: $data");

        setState(() {
          // 1) Parse the currentStreet => gameStage
          final streetStr = data["currentStreet"] as String? ?? "preflop";

          final bool isSameStreet = (streetStr == _lastStreet);

          switch (streetStr) {
            case "preflop":
              gameStage = GameStage.preHand;
              break;
            case "flop":
              gameStage = GameStage.flop;
              break;
            case "turn":
              gameStage = GameStage.turn;
              break;
            case "river":
              gameStage = GameStage.river;
              break;
            case "showdown":
              gameStage = GameStage.showdown;
              // Optionally set showAllCards = true here if you want
              break;
          }

          // 2) Update pot, currentBet, currentTurnIndex
          _actualPot = data["pot"] ?? 0;
          _displayedPot = _actualPot;
          _currentBet = data["currentBet"] ?? 0;
          _currentTurnIndex = data["currentTurnIndex"];
          final nextIndex = data["currentTurnIndex"];

          if (nextIndex == _myServerSeatIndex) {
            _userActionLock = false;
          } else {
            _userActionLock = true;
          }

          // 3) Sync burnedCards + communityCards
          final burnedArr = data["burnedCards"] as List? ?? [];
          for (int i = 0; i < 3; i++) {
            if (i < burnedArr.length) {
              final bc = burnedArr[i];
              burnedCards[i] = CardModel(bc["rank"] ?? "", bc["suit"] ?? "");
            } else {
              burnedCards[i] = CardModel('', '');
            }
          }
          final commArr = data["communityCards"] as List? ?? [];
          for (int i = 0; i < 5; i++) {
            if (i < commArr.length) {
              final cc = commArr[i];
              final rank = cc["rank"] ?? "";
              final suit = cc["suit"] ?? "";

              // MERGE:
              // Keep our local 'faceUp' if we already set it, else default false
              final wasFaceUp = communityCards[i].faceUp;
              communityCards[i] = CardModel(rank, suit, faceUp: wasFaceUp);
            } else {
              communityCards[i] = CardModel('', '', faceUp: false);
            }
          }

          // 4) Update isFolded, isAllIn, etc.
          final foldArr = data["isFolded"] as List? ?? [];
          final allInArr = data["isAllIn"] as List? ?? [];
          for (int i = 0; i < players.length; i++) {
            players[i].isFolded = (i < foldArr.length && foldArr[i] == true);
            players[i].isAllIn = (i < allInArr.length && allInArr[i] == true);
          }

          // _turnTimerController.stop();
          // _turnTimerController
          // ..reset();

          for (int i = 0; i < _pendingBets.length; i++) {
            _pendingBets[i] = 0;
            _amountsOnTable[i] = 0;
            players[i].contributedThisStreet = 0;
          }

          // 5) Update amountsOnTable and each player's contributedThisStreet
          for (int i = 0; i < _amountsOnTable.length; i++) {
            _amountsOnTable[i] = 0;
            players[i].contributedThisStreet = 0;
          }

          final amtList = data["amountsOnTable"] as List? ?? [];
          for (final obj in amtList) {
            final seat = obj["seat"] as int;
            final amt = obj["amt"] as int;
            if (seat >= 0 && seat < _amountsOnTable.length) {
              _amountsOnTable[seat] = amt;
              players[seat].contributedThisStreet = amt;
            }
          }

          // 6) Update each player's stack/name/sb/bb/dealer
          final updatedPlayers = data["players"] as List? ?? [];
          for (final upd in updatedPlayers) {
            final sNum = upd["seatNumber"] as int;
            if (sNum < 0 || sNum >= players.length) continue;

            players[sNum].name = upd["name"] ?? players[sNum].name;
            players[sNum].chips = upd["stack"] ?? players[sNum].chips;

            // Mark who is dealer/sb/bb if you like:
            // you can also store them as booleans in your Player model if desired
            final isDealer = (upd["dealer"] == true);
            final isSb = (upd["sb"] == true);
            final isBb = (upd["bb"] == true);
            if (isDealer) _dealerIndex = sNum;
            if (isSb) _smallBlindIndex = sNum;
            if (isBb) _bigBlindIndex = sNum;
          }

          // 7) Possibly reset your local _playerActions so you can display "..." or similar
          //    or leave them as-is. For example:

          // If we detect that the street *changed* since the last update, do a per-street reset.
          if (streetStr != _lastStreet) {
            _resetActionsForNewStreet();
            _lastStreet = streetStr;

            _runStreetFlipAnimation(streetStr);
          }

          final newHistory = data["handHistory"] as List<dynamic>?;
          if (newHistory != null) {
            // Option A: On each new hand, clear the client’s old list
            _updateHandHistory(newHistory);
          }

          // 8) If gameStage == GameStage.showdown => you might do showAllCards=true
          if (gameStage == GameStage.showdown) {
            final stillIn = players.where((p) => !p.isFolded).length;
            if (stillIn > 1) {
              showAllCards = true;
            } else {
              // If only one seat remains, do NOT set showAllCards=true
              showAllCards = false;
            }
            // Optionally trigger some local "showdown" animations
          }
          // —— INSERT DEBUG HERE ——
          debugPrint(
              '*** New street (“${data["currentStreet"]}”) — isAllIn statuses: ' +
                  players.map((p) => p.isAllIn).toList().toString());
          // Force re-render:
        });
      });

      socket.on("actionUpdate", (data) {
        print(">>> actionUpdate data: $data");
        // data is a Map, e.g.:
        // {
        //   "seatIndex": 2,
        //   "action": "raise",
        //   "amount": 500,
        //   "nextTurnIndex": 3,
        //   "pot": 1200,
        //   "currentBet": 500,
        //   "players": [
        //     {"seatNumber":0,"stack":"1000"},
        //     {"seatNumber":1,"stack":"All-in"},
        //     {"seatNumber":2,"stack":"1500"},
        //     {"seatNumber":3,"stack":"800"},
        //     ...
        //   ],
        //   "isFolded":[ false,true,false,false,...],
        //   "isAllIn": [ false,true,false,false,...],
        //   "amountsOnTable":[0,600,500,0, ...]
        // }

        setState(() {
          // 1) Read the simple fields
          final seatIndex = data["seatIndex"] as int;
          final action = data["action"] as String;
          final amount = data["amount"] as int;
          final nextIndex = data["nextTurnIndex"] as int?;
          final newPot = data["pot"] as int;
          final newBet = data["currentBet"] as int;

          // 2) Update your local 'table' fields
          _actualPot = newPot;
          _displayedPot =
              newPot; // or do a fancy chip-collection animation if you want
          _currentBet = newBet;
          _currentTurnIndex = nextIndex;

          final isMyTurnNow = _currentTurnIndex == _myServerSeatIndex;
          if (isMyTurnNow) {
            _hasAutoFoldedThisTurn = false;
          }

          // _turnTimerController
          //   ..stop()
          //   ..duration = Duration(milliseconds: _serverTurnDurationMs)
          //   ..reset();

          // if (_currentTurnIndex == _myServerSeatIndex) {
          //   _turnTimerController.forward();
          // }

          // 3) Update each seat from data["players"]
          final serverPlayers = data["players"];
          if (serverPlayers is List) {
            for (final sp in serverPlayers) {
              final sNum = sp["seatNumber"] as int;
              final stack = sp["stack"] as String; // e.g. "1000" or "All-in"
              // Now update your local seat’s chips:
              players[sNum].chips = stack;
            }
          }

          // 4) Sync isFolded[] & isAllIn[] from server
          final foldArr = data["isFolded"] as List<dynamic>;
          final allInArr = data["isAllIn"] as List<dynamic>;

          for (int i = 0; i < players.length; i++) {
            players[i].isFolded = (i < foldArr.length && foldArr[i] == true);
            players[i].isAllIn = (i < allInArr.length && allInArr[i] == true);
          }

          final amountsArr = data["amountsOnTable"] as List<dynamic>;
          for (int i = 0; i < _pendingBets.length; i++) {
            _pendingBets[i] = 0;
          }

          // First, reset local arrays
          for (int i = 0; i < _amountsOnTable.length; i++) {
            _amountsOnTable[i] = 0;
            players[i].contributedThisStreet = 0; // <<< add this!
          }

          // Then fill from the server
          for (final obj in amountsArr) {
            final seatIndex = obj["seat"] as int;
            final seatAmt = obj["amt"] as int;
            if (seatIndex >= 0 && seatIndex < _amountsOnTable.length) {
              _amountsOnTable[seatIndex] = seatAmt;
              _pendingBets[seatIndex] = seatAmt;
              players[seatIndex].contributedThisStreet =
                  seatAmt; // <<< add this!
            }
          }

          // 6) Optionally set the _playerActions[seatIndex] to something like:
          switch (action) {
            case "fold":
              _playerActions[seatIndex] = "Fold";
              break;
            case "check":
              _playerActions[seatIndex] = "Check";
              break;
            case "call":
              _playerActions[seatIndex] = "Call $amount";
              break;
            case "bet":
              // If they bet their entire stack => "All-in {amt}"
              final stackStr = players[seatIndex].chips.toLowerCase();
              if (stackStr == "all-in") {
                _playerActions[seatIndex] = "All-in $amount";
              } else {
                _playerActions[seatIndex] = "Bet $amount";
              }
              break;
            case "raise":
              // same logic
              final stackStr2 = players[seatIndex].chips.toLowerCase();
              if (stackStr2 == "all-in") {
                _playerActions[seatIndex] = "All-in $amount";
              } else {
                // or "Raise to (their contributedThisStreet)" if you prefer
                _playerActions[seatIndex] = "Raise $amount";
              }
              break;
          }

          final newHistory = data["handHistory"] as List<dynamic>?;
          if (newHistory != null) {
            // Option A: On each new hand, clear the client’s old list
            _updateHandHistory(newHistory);
          }

          // 7) If the nextTurnIndex is MY seat => enable my action UI
          //    else disable it (or let the seat logic handle that).
          print(_currentTurnIndex);
          print(_myServerSeatIndex);
          if (_currentTurnIndex == _myServerSeatIndex) {
            _userActionLock = false;
          } else {
            _userActionLock = true;
          }

          // 8) Done
        });
      });

      socket.on("startHand", (data) {
        print(">>> startHand received:");
        // Example data structure:
        // {
        //   "players": [
        //     {
        //       "seatNumber": 0,
        //       "name": "Alice",
        //       "stack": "2000",
        //       "cards": [
        //         {"rank": "K", "suit": "Hearts"},
        //         {"rank": "6", "suit": "Diamonds"}
        //       ],
        //       "dealer": true,
        //       "sb": false,
        //       "bb": true,
        //       "eliminated": false
        //     },
        //     ...
        //   ],
        //   "pot": 0,
        //   "burnedCards": [],
        //   "communityCards": [],
        //   "currentBet": 200,
        //   "currentTurnIndex": 1,
        //   "deck": [...],
        //   "isFolded": [ false, false, ...],
        //   "isAllIn":   [ false, false, ...],
        //   "amountsOnTable": [ 100, 200, ...],
        //   "handHistory": [...],
        //   ...
        // }

        final updatedPlayers = data["players"];
        if (updatedPlayers is List) {
          setState(() {
            // Clear old side-pot data, animations, etc.
            _sidePots.clear();
            _builtPotChopShares.clear();
            _payoutAnimatingPotIndex = 0;
            _payoutController.reset();

            // Reset pot & countdown
            _displayedPot = 0;
            _showCountdown = false;
            _countdownText = "";

            // Mark game as started
            _isGameStarted = true;
            showAllCards = false;
            gameStage = GameStage.preHand;
            _historyPanelState = HistoryPanelState.semiOpen;

            // Clear each player's local 'hand'
            for (int i = 0; i < players.length; i++) {
              players[i].hand.clear();
            }

            // Possibly update our local handHistory from server
            final newHistory = data["handHistory"] as List<dynamic>?;
            if (newHistory != null) {
              _updateHandHistory(newHistory);
            }

            // Temporary variables for (server) seat indexes
            int? dealerIndexServer;
            int? smallBlindIndexServer;
            int? bigBlindIndexServer;

            // We’ll set these after we parse the players
            _dealerIndex = null;
            _smallBlindIndex = null;
            _bigBlindIndex = null;

            _serverTurnDurationMs = data["turnDurationMs"] as int;

            _hasAutoFoldedThisTurn = false;
            _turnTimerController
              ..stop()
              ..duration = Duration(milliseconds: data["turnDurationMs"])
              ..reset()
              ..forward();

            // ----------------------
            // Populate seat data
            // ----------------------
            for (final p in updatedPlayers) {
              final seatIndex = p["seatNumber"] as int;

              // Overwrite local seat data
              players[seatIndex].name = p["name"] ?? "";
              players[seatIndex].chips = p["stack"] ?? "0";
              players[seatIndex].eliminated = (p["eliminated"] == true);

              // If eliminated => seat inactive, folded, label "Eliminated"
              if (players[seatIndex].eliminated) {
                //seatActive[seatIndex]      = false;
                players[seatIndex].isFolded = true;
                _playerActions[seatIndex] = "Eliminated";
              } else {
                seatActive[seatIndex] = true;
                // we’ll set folded/allIn below from isFolded[], isAllIn[] arrays
              }

              // Check if this seat is dealer / sb / bb
              if (p["dealer"] == true) {
                dealerIndexServer = seatIndex;
              }
              if (p["sb"] == true) {
                smallBlindIndexServer = seatIndex;
              }
              if (p["bb"] == true) {
                bigBlindIndexServer = seatIndex;
              }
            }

            // Convert server seat indexes to local
            if (dealerIndexServer != null) {
              _dealerIndex = dealerIndexServer;
            }
            if (smallBlindIndexServer != null) {
              _smallBlindIndex = smallBlindIndexServer;
            }
            if (bigBlindIndexServer != null) {
              _bigBlindIndex = bigBlindIndexServer;
            }

            // ----------------------
            // Burned & community cards
            // ----------------------
            final dynamicBurned = data["burnedCards"] as List? ?? [];
            burnedCards = List.generate(3, (i) {
              if (i < dynamicBurned.length) {
                final bc = dynamicBurned[i];
                return CardModel(bc["rank"] ?? "", bc["suit"] ?? "");
              }
              return CardModel('', '');
            });

            final dynamicComm = data["communityCards"] as List? ?? [];
            communityCards = List.generate(5, (i) {
              if (i < dynamicComm.length) {
                final cc = dynamicComm[i];
                return CardModel(cc["rank"] ?? "", cc["suit"] ?? "");
              }
              return CardModel('', '');
            });

            // ----------------------
            // Pot, bet, turn index
            // ----------------------
            _actualPot = data["pot"] ?? 0;
            _displayedPot = _actualPot;
            _currentBet = data["currentBet"] ?? 0;
            _currentTurnIndex = data["currentTurnIndex"];

            // _turnTimerController.stop();
            // _turnTimerController
            // .reset();

            if (_currentTurnIndex == _myServerSeatIndex) {
              _userActionLock = false;
            } else {
              _userActionLock = true;
            }

            // ----------------------
            // isFolded, isAllIn, amountsOnTable
            // ----------------------
            final ifArray = data["isFolded"] as List? ?? [];
            final iaArray = data["isAllIn"] as List? ?? [];
            final newArr = data["amountsOnTable"] as List? ?? [];

            for (int i = 0; i < players.length; i++) {
              players[i].isFolded = (i < ifArray.length && ifArray[i] == true);
              players[i].isAllIn = (i < iaArray.length && iaArray[i] == true);
            }
            for (int i = 0; i < _amountsOnTable.length; i++) {
              _amountsOnTable[i] = 0;
              players[i].contributedThisStreet = 0;
            }
            for (final obj in newArr) {
              final seatNum = obj["seat"] as int;
              final seatAmt = obj["amt"] as int;
              if (seatNum >= 0 && seatNum < _amountsOnTable.length) {
                _amountsOnTable[seatNum] = seatAmt;
                players[seatNum].contributedThisStreet = seatAmt;
              }
            }

            // leftover deck (if you want animations)
            final leftoverDeck = data["deck"] as List? ?? [];
            deck.clear();
            for (final d in leftoverDeck) {
              final rank = d["rank"] ?? "";
              final suit = d["suit"] ?? "";
              deck.add(CardModel(rank, suit));
            }

            // Show blinds in the UI
            for (int i = 0; i < _playerActions.length; i++) {
              if (players[i].eliminated) {
                _playerActions[i] = "Eliminated";
              } else {
                _playerActions[i] = "...";
              }
            }
            _handHistory.add(" ");

            // If server says sb/bb => display “SB ___” or “BB ___”
            for (final p in updatedPlayers) {
              final sIndex = p["seatNumber"] as int;
              final posted = _amountsOnTable[sIndex];
              if (p["sb"] == true && posted > 0) {
                _playerActions[sIndex] = "SB $posted";
              } else if (p["bb"] == true && posted > 0) {
                _playerActions[sIndex] = "BB $posted";
              }
            }

            // ----------------------
            // Build dealing queue
            // ----------------------
            _cardsToDeal.clear();

            var foundDealer = updatedPlayers
                .firstWhere((p) => p["dealer"] == true, orElse: () => null);

            if (foundDealer == null) {
              print("No dealer found; defaulting to seat 0");
              foundDealer = updatedPlayers.first;
            }
            int actualDealerIndex = foundDealer["seatNumber"] as int;

            // Then build seatNumbers normally:
            final seatNumbers =
                updatedPlayers.map<int>((p) => p["seatNumber"] as int).toList();
            final dealerPos = seatNumbers.indexOf(actualDealerIndex);
            if (dealerPos < 0) {
              print("Dealer index not found in seatNumbers.");
              return;
            }

            // Rebuild dealingOrder, skipping no one yet:
            final dealingOrder = <int>[];
            for (int i = 1; i <= seatNumbers.length; i++) {
              final seatNum = seatNumbers[(dealerPos + i) % seatNumbers.length];
              dealingOrder.add(seatNum);
            }

            // Finally the two-pass deal, skipping eliminated seats:
            _cardsToDeal.clear();
            for (int pass = 0; pass < 2; pass++) {
              for (final seatNum in dealingOrder) {
                final seatObj = updatedPlayers.firstWhere(
                    (p) => p["seatNumber"] == seatNum,
                    orElse: () => null);
                if (seatObj == null) continue;

                // Skip if seatObj is eliminated
                if (seatObj["eliminated"] == true) continue;

                final seatCards = seatObj["cards"] as List? ?? [];
                if (pass < seatCards.length) {
                  final cardData = seatCards[pass];
                  final rank = cardData["rank"] ?? "";
                  final suit = cardData["suit"] ?? "";
                  final cardModel = CardModel(rank, suit);
                  _cardsToDeal.add((seatNum, cardModel));
                }
              }
            }

            // At the end:
            _isDealing = true;
            _dealingIndex = 0;
            _dealNextCard();
          });
        }
      });
    }

    // 5) Now actually connect
    //socket.connect();

    // 6) Also call _loadTableFromServer() to get table details
    _loadTableFromServer().then((_) {
      // now `players` and `seatActive` are correctly sized & populated
      if (!socket.connected) {
        socket.connect();
      }

      // only after we're loaded do we actually join
      final playerId = html.window.localStorage['playerId'];
      if (playerId != null) {
        socket.emit("playerJoinedTable", {
          "roomId": widget.roomId,
          "playerId": playerId,
        });
      }
    });

    deck = _buildDeck();

    // Initialize seat 0 as user-controlled by default
    //_playerControls[0] = true;

    // _isFolded = List.filled(players.length, false);
    // _isAllIn  = List.filled(players.length, false);

    _turnTimerController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: _serverTurnDurationMs),
    )
      ..addListener(() => setState(() {}))
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed && !_hasAutoFoldedThisTurn) {
          if (_currentTurnIndex == _myServerSeatIndex) {
            _hasAutoFoldedThisTurn = true;
            setState(() {
              _showSlider = false;
              _sliderIsRaise = false;
            });
            socket.emit("playerAction", {
              "auto": true,
              "roomId": _roomId,
              "seatIndex": _currentTurnIndex,
              "action": "fold",
            });
          }
        }
      });

    // Deal controller
    _dealController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _dealAnimation = Tween<Offset>(begin: Offset.zero, end: Offset.zero)
        .animate(_dealController)
      ..addListener(() {
        setState(() => _flyingCardOffset = _dealAnimation.value);
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _onSingleDealComplete();
        }
      });

    // Chip-collection controller
    _chipCollectController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )
      ..addListener(() {
        setState(() {});
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          // 1) First setState: zero out bets, stop showing them
          setState(() {
            // Immediately remove the bet chips from the UI
            _isCollectingBets = false;

            // Reset pending bets so they're no longer drawn
            for (int i = 0; i < players.length; i++) {
              _pendingBets[i] = 0;
            }

            int totalCollected = 0;
            for (int i = 0; i < players.length; i++) {
              totalCollected += _amountsOnTable[i];
              _amountsOnTable[i] = 0;
            }
          });

          // 2) After the above repaint, bump the actual pot (and displayed pot)
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              // Tally the newly collected chips into _actualPot
              int totalCollected = 0;
              for (int i = 0; i < players.length; i++) {
                totalCollected += _amountsOnTable[i];
                _amountsOnTable[i] = 0;
              }

              // Now update your actual pot
              _actualPot += totalCollected;

              // Finally update the pot label AFTER the chips have disappeared
              _displayedPot = _actualPot;

              // Reset or stop the controller
              _chipCollectController.reset();
            });

            // If you need to tell the server “we finished collecting”:
            socket.emit("chipsCollected", {"roomId": _roomId});
          });
        }
      });

    // Payout animation controller
    _payoutController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1600),
    )
      ..addListener(() {
        setState(() {
          _payoutAnimationValue = _payoutController.value;
        });
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _payoutController.reset();
          _onSinglePayoutAnimationComplete();
        }
      });
  }

  void _startTurnTimer(Map<String, dynamic> data) {
    // pull in the server’s official turn length
    //_serverTurnDurationMs = data["turnDurationMs"] as int;
    final turnMs = data["turnDurationMs"] as int? ?? 20000;

    setState(() {
      _currentTurnIndex = data["seatIndex"] as int?;
      _hasAutoFoldedThisTurn = false;

      if (turnMs == 0) {
        // "Unlimited" → don’t run timer
        // e.g. set _serverTurnDurationMs=0 and skip .forward()
        _serverTurnDurationMs = 0;
        _turnTimerController.stop();
        _turnTimerController.duration = const Duration(milliseconds: 1);
        _turnTimerController.reset();
      } else {
        _serverTurnDurationMs = turnMs;
        _turnTimerController.stop();
        _turnTimerController.duration = Duration(milliseconds: turnMs);
        _turnTimerController.reset();
        _turnTimerController.forward();
      }
    });
  }

  Future<void> _runStreetFlipAnimation(String street) async {
    if (street == "flop") {
      // Lock out user:
      setState(() => _userActionLock = true);

      // Mark the 3 flop cards faceDown initially
      communityCards[0].faceUp = false;
      communityCards[1].faceUp = false;
      communityCards[2].faceUp = false;
      setState(() {}); // cause immediate repaint

      // Wait 1 second
      await Future.delayed(const Duration(seconds: 1));

      // Flip them faceUp
      communityCards[0].faceUp = true;
      communityCards[1].faceUp = true;
      communityCards[2].faceUp = true;

      // Unlock user
      _userActionLock = false;
      setState(() {});
    } else if (street == "turn") {
      setState(() => _userActionLock = true);

      communityCards[3].faceUp = false;
      setState(() {});
      await Future.delayed(const Duration(seconds: 1));

      communityCards[3].faceUp = true;

      _userActionLock = false;
      setState(() {});
    } else if (street == "river") {
      setState(() => _userActionLock = true);

      communityCards[4].faceUp = false;
      setState(() {});
      await Future.delayed(const Duration(seconds: 1));

      communityCards[4].faceUp = true;

      _userActionLock = false;
      setState(() {});
    }
    // if street == 'showdown', you might do showAllCards, etc.
  }

  void _resetActionsForNewStreet() {
    for (int i = 0; i < players.length; i++) {
      if (players[i].eliminated) {
        _playerActions[i] = "Eliminated";
      } else if (players[i].isFolded) {
        _playerActions[i] = "Fold";
      } else if (players[i].isAllIn) {
        // If you want "All-in" or keep the old label
        // _playerActions[i] = "All-in";
      } else {
        _playerActions[i] = "...";
      }
    }
  }

  @override
  @override
  void dispose() {
    // Clear all socket listeners to prevent memory leaks
    socket.off("playerListUpdated");
    socket.off("removedFromTable");
    socket.off("duplicateSession");
    socket.off("turnStart");
    socket.off("knockedOut");
    socket.off("gameOver");
    socket.off("inactivityBoot");
    socket.off("nextHandCountdown");
    socket.off("showdownResults");
    socket.off("info");
    socket.off("collectChipsAnimation");
    socket.off("joinedMidHand");
    socket.off("streetUpdate");
    socket.off("actionUpdate");
    socket.off("startHand");

    // Cleanup controllers
    _scrollCtrl.dispose();
    _historyScrollController.dispose();
    _turnTimerController.dispose();
    _dealController.dispose();
    _chipCollectController.dispose();
    _payoutController.dispose();

    // Cancel any active timers
    _countdownTimer?.cancel();

    // Disconnect socket cleanly
    if (socket.connected) {
      socket.disconnect();
    }

    // Clear any references that might hold memory
    _potLabelKeys.clear();
    _potLabelOffsets.clear();
    _currentPotChopItems.clear();
    _builtPotChopShares.clear();

    super.dispose();
  }

  bool _dataLoaded = false;

  Future<void> _loadTableFromServer() async {
    setState(() {
      // Clear existing arrays while loading
      players = [];
      seatActive = [];
      _dataLoaded = false;
    });

    try {
      // 1) Fetch table data from your server API
      final data = await Api.getTableData(_roomId);
      print(data); // <-- original debug print from your code

      if (data != null) {
        // 2) Create a blank List<Player> for seatCount
        final seatCount = data['seatCount'] as int;
        setState(() {
          players = List.generate(
            seatCount,
            (index) => Player(
              id: "",
              name: "",
              chips: "0",
              seatNumber: index, // <--- use 'index' from the callback param
            ),
          );
          seatActive = List.filled(seatCount, false);

          // Also rebuild arrays that must match seatCount
          for (final p in players) {
            p.isFolded = false;
            p.isAllIn = false;
          }
          _playerActions = List.filled(seatCount, "...");
          _amountsOnTable = List.filled(seatCount, 0);
          _playerControls = List.filled(seatCount, false);
          _pendingBets = List.filled(seatCount, 0);
          _handStartingStacks = List.filled(seatCount, 0);

          // 3) If the server returns a "players" list, populate each seat
          final dynamicPlayers = data['players'];
          if (dynamicPlayers is List) {
            for (final pd in dynamicPlayers) {
              final seatNumber = pd['seatNumber'] as int;
              if (seatNumber >= 0 && seatNumber < seatCount) {
                players[seatNumber].name = pd['name'] ?? '';
                players[seatNumber].chips = pd['stack'] ?? '0';
                seatActive[seatNumber] = true;
              }
            }
          }

          // 4) Optionally parse blindLevel (e.g. "100/200") from server
          final serverBlind = data['blindLevel'];
          if (serverBlind is String && serverBlind.contains('/')) {
            final parts = serverBlind.split('/');
            if (parts.length == 2) {
              _smallBlind = int.tryParse(parts[0]) ?? 100;
              _bigBlind = int.tryParse(parts[1]) ?? 200;
            }
          }

          // Done fetching => mark data as loaded
          _dataLoaded = true;
        });

        // (Optional) If you have logic that calls socket.emit("joinRoom"), do it here:
        // _attemptSocketJoin(data);
      } else {
        // If the server returned null, mark as loaded so we don't spin forever
        setState(() => _dataLoaded = true);
      }
    } catch (e) {
      // If there's an error, log it and mark data as loaded
      debugPrint("Failed to load table data: $e");
      setState(() => _dataLoaded = true);
    }

    // ================================
    // RESTORE USER’S SEAT FROM STORAGE
    // ================================
    final storedSeatStr = html.window.localStorage['seatPosition'];
    if (storedSeatStr != null) {
      final storedSeatIndex = int.tryParse(storedSeatStr);
      if (storedSeatIndex != null &&
          storedSeatIndex >= 0 &&
          storedSeatIndex < players.length) {
        setState(() {
          _myServerSeatIndex = storedSeatIndex;
          seatActive[_myServerSeatIndex!] = true; // Mark seat as active
          _playerControls[_myServerSeatIndex!] =
              true; // Let the user control that seat
        });
      }
    }
  }

  bool get iAmHost {
    // If “isCreator” is stored as 'true', return true
    final creatorValue = html.window.localStorage['isCreator'];
    return (creatorValue == 'true');
  }

  void _scrollToHistoryBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_historyScrollController.hasClients) {
        _historyScrollController.jumpTo(
          _historyScrollController.position.maxScrollExtent,
        );
      }
    });
  }

  void _copyHistory() {
    if (_handHistory.isEmpty) return;
    final fullText = _handHistory.join("\n");
    Clipboard.setData(ClipboardData(text: fullText));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text("History copied to clipboard!")),
    );
  }

  void _toggleHistory() {
    setState(() {
      switch (_historyPanelState) {
        case HistoryPanelState.closed:
          _historyPanelState = HistoryPanelState.semiOpen;
          break;
        case HistoryPanelState.semiOpen:
          _historyPanelState = HistoryPanelState.fullyOpen;
          break;
        case HistoryPanelState.fullyOpen:
          _historyPanelState = HistoryPanelState.closed;
          break;
      }
    });
    if (_historyPanelState != HistoryPanelState.closed) {
      _scrollToHistoryBottom();
    }
  }

  /// Intelligently updates hand history to avoid resetting the ActionLogModal
  /// Appends new entries if history is growing incrementally, replaces entirely if new hand
  void _updateHandHistory(List<dynamic>? newHistoryData) {
    if (newHistoryData == null) return;

    final newHistory = newHistoryData.map((e) => e.toString()).toList();

    // If current history is empty, just set the new history
    if (_handHistory.isEmpty) {
      _handHistory.addAll(newHistory);
      return;
    }

    // If new history is shorter, it's likely a new hand - replace entirely
    if (newHistory.length < _handHistory.length) {
      _handHistory.clear();
      _handHistory.addAll(newHistory);
      return;
    }

    // If new history is longer, check if it's an incremental update
    if (newHistory.length > _handHistory.length) {
      // Check if the existing history matches the beginning of the new history
      bool isIncremental = true;
      for (int i = 0; i < _handHistory.length; i++) {
        if (i >= newHistory.length || _handHistory[i] != newHistory[i]) {
          isIncremental = false;
          break;
        }
      }

      if (isIncremental) {
        // Just add the new entries
        final newEntries = newHistory.sublist(_handHistory.length);
        _handHistory.addAll(newEntries);
        return;
      }
    }

    // If we get here, the histories don't match incrementally - replace entirely
    _handHistory.clear();
    _handHistory.addAll(newHistory);
  }

  // =========================
  // DECK / CHIPS / BUILDERS
  // =========================

  List<CardModel> _buildDeck() {
    final ranks = [
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      'J',
      'Q',
      'K',
      'A'
    ];
    final suits = ['Clubs', 'Diamonds', 'Hearts', 'Spades'];
    final newDeck = <CardModel>[];
    for (final s in suits) {
      for (final r in ranks) {
        newDeck.add(CardModel(r, s));
      }
    }
    return newDeck;
  }

  int _parseChips(String str) {
    if (str.toLowerCase().contains("all-in")) {
      return 0;
    }
    return int.tryParse(str) ?? 0;
  }

  List<int> _getActiveSeats() {
    final list = <int>[];
    for (int i = 0; i < seatActive.length; i++) {
      if (seatActive[i]) {
        list.add(i);
      }
    }
    return list;
  }

  // =========================
  // DEALING
  // =========================

  void _dealNextCard() {
    final totalToDeal = _cardsToDeal.length;
    if (_dealingIndex >= totalToDeal) {
      // done dealing
      setState(() => _isDealing = false);
      _runCalculateAllThenContinue(); // if you want
      return;
    }

    // Grab the actual seatIndex from _cardsToDeal
    final (seatIndex, card) = _cardsToDeal[_dealingIndex];

    final center = _tableCenterOffset();
    final seatPos = _dealTargetOffset(seatIndex);

    // Stop any previous anim, reset
    _dealController.stop();
    _dealController.reset();

    final tween = Tween<Offset>(begin: center, end: seatPos);
    _dealAnimation = tween.animate(
        CurvedAnimation(parent: _dealController, curve: Curves.easeOut));

    _dealController.forward();
  }

  void _onSingleDealComplete() {
    // Grab the tuple (seatIndex, card) from our queue
    final (seatIndex, card) = _cardsToDeal[_dealingIndex];

    // If this is my seat, reveal it:
    if (seatIndex == _myServerSeatIndex) {
      card.faceUp = true;
    }

    // Now put the card into that player's hand
    setState(() {
      players[seatIndex].hand.add(card);
    });

    // Move on to the next card
    _dealingIndex++;
    if (_dealingIndex < _cardsToDeal.length) {
      _dealNextCard();
    } else {
      // We’re done dealing both hole cards
      setState(() => _isDealing = false);

      // now fire the timer if we buffered it
      if (_pendingTurnStartData != null) {
        _startTurnTimer(_pendingTurnStartData!);
        _pendingTurnStartData = null;
      }
      // e.g. do next steps or let server logic continue
    }
  }

  Offset _tableCenterOffset() {
    final w = MediaQuery.of(context).size.width;
    final h = MediaQuery.of(context).size.height;
    return Offset(w / 2, h / 2);
  }

  int offsetForSeat(int seatIndex) {
    // If "mySeat" is the one that should appear at offset=0:
    // offset = seatIndex - mySeat
    final seatCount = players.length;
    return (seatIndex - _myServerSeatIndex! + seatCount) % seatCount;
  }

  Offset _dealTargetOffset(int serverSeatIndex) {
    final localIndex = serverToLocal(serverSeatIndex);
    final tabCenter = _playerTabCenterOffset(localIndex);

    final isMobile = MediaQuery.of(context).size.width < 600;
    const baseTabHeight = 160.0;
    final scale =
        isMobile ? (localIndex == _myServerSeatIndex ? 0.8 : 0.55) : 1.0;
    final tabHeight = baseTabHeight * scale;

    final cardWidth = _scaledSmallCardWidth;
    final cardHeight = _scaledSmallCardHeight;
    final dx = tabCenter.dx - cardWidth / 2;
    final dy = tabCenter.dy + tabHeight / 2 - cardHeight / 2;

    // move end point up by 15px:
    return Offset(dx, dy - 60);
  }

  Offset _seatCardOffset(int seatIndex) {
    final offset = offsetForSeat(seatIndex);
    final w = MediaQuery.of(context).size.width;
    final h = MediaQuery.of(context).size.height;
    final cx = w / 2;
    final cy = h / 2;
    final rx = _seatCircleRx;
    final ry = _seatCircleRy;

    final angle = (math.pi / 2) + offset * (2 * math.pi / seatCount);
    final sx = cx + rx * math.cos(angle);
    final sy = cy + ry * math.sin(angle);

    // e.g. slight shift so the card is near that seat
    final cardDist = 110.0;
    final cardX = sx - cardDist * math.cos(angle);
    final cardY = sy - cardDist * math.sin(angle);

    return Offset(cardX - 25, cardY - 35);
  }

  // =========================
  // BETTING + ACTION
  // =========================

  int _neededToCall(int seatIndex) {
    final p = players[seatIndex];
    final contributed = p.contributedThisStreet;
    final needed = _currentBet - contributed;
    return (needed < 0) ? 0 : needed;
  }

  Offset _seatBetOffset(int seatIndex) {
    // 1) The same ellipse math to find seat's (sx, sy)
    final w = MediaQuery.of(context).size.width;
    final h = MediaQuery.of(context).size.height;
    final cx = w / 2;
    final cy = h / 2;

    // seatIndex => angle => seat x,y
    // or if you are rotating so your seat is offset=0, you do offsetForSeat(seatIndex)
    final offset = offsetForSeat(seatIndex);
    final angle = (math.pi / 2) + offset * (2 * math.pi / players.length);
    final sx = cx + 370 * math.cos(angle);
    final sy = cy + 270 * math.sin(angle);

    // 2) Do the same fraction offset + shift from your bet drawing code
    const fraction = 0.42;
    final betX = sx + fraction * (cx - sx) - 35;
    final betY = sy + fraction * (cy - sy) - 10;

    // 3) If you have the "isMain" shift for seat 0, replicate that:
    final isMainSeat = (seatIndex == _myServerSeatIndex);
    final finalY = isMainSeat ? (betY - _mainPlayerExtraYOffset) : betY;

    return Offset(betX, finalY);
  }

  /// Replaces your existing _buildCurrentPotChopItems method
  void _buildCurrentPotChopItems(int potIndex) {
    _currentPotChopItems.clear();
    final sp = _sidePots[potIndex];
    final chunkList = _builtPotChopShares[potIndex] ?? [];
    final rawStart = _potLabelOffsets[potIndex] ?? _tableCenterOffset();
    final startOffset = rawStart.translate(-15.0, 0.0);

    for (final chunk in chunkList) {
      final seatIndex = chunk["seat"] as int;
      final amt = chunk["amount"] as double;
      final labelText = "Pot ${potIndex + 1}: \$${amt.toStringAsFixed(0)}";

      // Convert the server seat index to local
      final localIndex = serverToLocal(seatIndex);

      // Pass the local index to our playerTab offset function
      final seatCenter = _playerTabCenterOffset(localIndex);

      const horizontalShift = Offset(-50, 0);
      final endOffset = seatCenter + horizontalShift;

      _currentPotChopItems.add(_ChopPayoutItem(
        seatIndex: seatIndex, // You could store localIndex if you prefer
        label: labelText,
        start: startOffset,
        end: endOffset,
      ));
    }
  }

  /// Replaces your existing _playerTabCenterOffset method
  Offset _playerTabCenterOffset(int localIndex) {
    // IMPORTANT: We removed the line that did:
    //    final localIndex = serverToLocal(serverSeatIndex);
    // so we do NOT double-convert now.

    final w = MediaQuery.of(context).size.width;
    final h = MediaQuery.of(context).size.height;
    final cx = w / 2;
    final cy = h / 2;
    final rx = _seatCircleRx;
    final ry = _seatCircleRy;

    // Use the 'localIndex' directly in the angle calculation
    final angle = (math.pi / 2) + localIndex * (2 * math.pi / players.length);

    final sx = cx + rx * math.cos(angle);
    final sy = cy + ry * math.sin(angle);

    final isMain = (localIndex == 0);
    final leftX = sx - (isMain ? 70 : 60);
    final topY = sy - (isMain ? 60 + _mainPlayerExtraYOffset : 60);

    const tabW = 120.0;
    const tabH = 160.0;
    final centerX = leftX + tabW / 2;
    final centerY = topY + tabH / 2;

    return Offset(centerX, centerY);
  }

  void _onSinglePayoutAnimationComplete() {
    _payoutController.reset();
    _applyCurrentPotChips();
    _payoutAnimatingPotIndex++;
    if (_payoutAnimatingPotIndex >= _sidePots.length) {
      _isAnimatingPayout = false;
      _finalizePayouts();
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        for (int i = 0; i < _potLabelKeys.length; i++) {
          _potLabelKeys[i].currentState?._measureAndReport();
        }
        if (!mounted) return;
        setState(() {
          _buildCurrentPotChopItems(_payoutAnimatingPotIndex);
          _payoutController.forward();
        });
      });
    }
  }

  void _applyCurrentPotChips() {
    final chunkList = _builtPotChopShares[_payoutAnimatingPotIndex] ?? [];
    for (final chunk in chunkList) {
      final seatIndex = chunk["seat"] as int;
      final amt = chunk["amount"] as double;
      final currentStack = _parseChips(players[seatIndex].chips);
      final newStack = (currentStack + amt).round();
      players[seatIndex].chips = newStack.toString();
    }
  }

  Future<void> _finalizePayouts() async {
    for (final seatIndex in _seatPendingChips.keys) {
      final finalStack = _seatPendingChips[seatIndex]!.round();
      players[seatIndex].chips = finalStack > 0 ? finalStack.toString() : "0";
      if (finalStack == 0) {
        _playerActions[seatIndex] = "Eliminated";
      }
    }

    // Manually reset chip-collection/payout animations:
    _chipCollectController.reset();
    _payoutController.reset();
    _isCollectingBets = false;
    _isAnimatingPayout = false;
    _currentPotChopItems.clear();

    // Force all seats' chip offsets to start back at the seat (or wherever you want them).
    for (int i = 0; i < players.length; i++) {
      _pendingBets[i] = 0;
      _chipOffsets[i] = _seatCardOffset(i);
    }

    // Basic stats
    await _updateBasicStats();
    await _saveHandRecordToHive();
    setState(() {
      gameStage = GameStage.showdown;
    });
  }

  Future<void> _updateBasicStats() async {
    for (int i = 0; i < players.length; i++) {
      if (!seatActive[i]) continue;
      final pstats = players[i].playerStats;
      if (pstats == null) continue;
      pstats.handsPlayed++;
      if (_seatsSawFlop.contains(i)) {
        pstats.handsSeenFlop++;
      }
      if (_seatsSawTurn.contains(i)) {
        pstats.handsSeenTurn++;
      }
      if (_seatsSawRiver.contains(i)) {
        pstats.handsSeenRiver++;
      }
      if (_showdownMap.containsKey(i)) {
        pstats.handsSawShowdown++;
      }
      if (_winnerIndex == i || _potWinnerSeats.any((ws) => ws.contains(i))) {
        pstats.handsWon++;
      }
      final starting = _handStartingStacks[i];
      final ending = _parseChips(players[i].chips);
      final netDelta = ending - starting;
      pstats.totalProfit += netDelta;
      if (netDelta > pstats.biggestPotWon) {
        pstats.biggestPotWon = netDelta.toDouble();
      }
      if (netDelta < 0 && netDelta.abs() > pstats.biggestPotLost) {
        pstats.biggestPotLost = netDelta.abs().toDouble();
      }
    }
  }

  void _showGameOverModal(String winnerName, bool isWinner) {
    print(
        '_showGameOverModal called with winner: $winnerName, isWinner: $isWinner');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: const Color.fromARGB(255, 46, 45, 45),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: 340,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 46, 45, 45),
              borderRadius: BorderRadius.circular(8),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  offset: Offset(0, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title
                const Text(
                  "Game Over!",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color.fromARGB(255, 4, 159, 206),
                  ),
                ),
                const SizedBox(height: 16),

                // Body text - different for winner vs loser
                Text(
                  isWinner
                      ? "Congratulations! You won the game!"
                      : "Player $winnerName is the winner!",
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),

                // OK button
                ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // close dialog
                      // Optionally navigate away from table or do something else
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromARGB(255, 4, 159, 206),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: const Text(
                      "OK",
                      style: TextStyle(color: Colors.white),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showKnockedOutModal() {
    showDialog<void>(
      context: context,
      barrierDismissible: false, // user must choose an action
      builder: (ctx) {
        return Dialog(
          backgroundColor: const Color.fromARGB(255, 46, 45, 45),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: 340,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 46, 45, 45),
              borderRadius: BorderRadius.circular(8),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  offset: Offset(0, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  "You've Been Knocked Out",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color.fromARGB(255, 4, 159, 206),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  "Unfortunately, you've lost all your chips.",
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: null, // or implement if you want Re-Buys
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromARGB(255, 4, 159, 206),
                  ),
                  child: const Text(
                    "Re-buy? (disabled)",
                    style: TextStyle(
                      color: Color.fromARGB(221, 141, 141, 141),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ElevatedButton.icon(
                      onPressed: _onExitTablePressed,
                      icon: const Icon(
                        Icons.exit_to_app,
                        color: Color.fromARGB(221, 255, 255, 255),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      label: const Text(
                        "Exit Table",
                        style: TextStyle(
                          color: Color.fromARGB(221, 255, 255, 255),
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(ctx).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color.fromARGB(255, 4, 159, 206),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: const Text(
                        "Stay & Watch",
                        style: TextStyle(
                          color: Color.fromARGB(221, 255, 255, 255),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    ).then((_) {
      // once the “you’re KO’d” dialog is fully dismissed…
      // print(_countActiveSeats() == 1);
      // print('hello');
      // if (_countActiveSeats() == 1) {
      //   // find the last player with chips:
      //   final winner = players.firstWhere((p) => int.parse(p.chips) > 0).name;
      //   _showGameOverModal(winner);
      // }
    });
  }

// Add these methods inside your _PokerTablePageState class

  /// Shows a styled confirmation dialog before leaving the table
  void _onExitTablePressed() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) {
        return Dialog(
          backgroundColor: const Color.fromARGB(255, 46, 45, 45),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: 340,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 46, 45, 45),
              borderRadius: BorderRadius.circular(8),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  offset: Offset(0, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  "Confirm Exit",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color.fromARGB(255, 4, 159, 206),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  "Are you sure you want to leave the table?",
                  style: TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ElevatedButton(
                      onPressed: () => Navigator.of(ctx).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: const Text(
                        "No",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(ctx).pop();
                        _leaveTable();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color.fromARGB(255, 4, 159, 206),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: const Text(
                        "Yes",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Encapsulates the actual leave-table logic
  void _leaveTable() {
    // Close any other open dialogs (e.g., the Knocked Out modal)
    Navigator.of(context).pop();

    final mySeat = _myServerSeatIndex;
    if (mySeat != null) {
      setState(() {
        seatActive[mySeat] = false;
      });
      // Inform server
      socket.emit("leaveSeat", {
        "roomId": _roomId,
        "seatIndex": mySeat,
      });
    }

    // Navigate back to lobby or home
    Navigator.of(context).pushReplacementNamed("/");
  }

  Future<void> _saveHandRecordToHive() async {
    final handId =
        _generatedHandId ?? "hand_${DateTime.now().millisecondsSinceEpoch}";
    final seatHoleCards = players.map((p) {
      return p.hand.map((c) => _cardString(c)).toList();
    }).toList();

    final record = HandRecord(
      handId: handId,
      timestamp: DateTime.now(),
      tableId: "SimTable1",
      maxPlayers: players.length,
      playerNames: players.map((p) => p.name).toList(),
      seatActive: List.from(seatActive),
      startingStacks: List<int>.from(_handStartingStacks),
      finalStacks: players.map((p) => p.chips).toList(),
      holeCards: seatHoleCards,
      dealerIndex: _dealerIndex ?? 0,
      smallBlindIndex: _smallBlindIndex ?? 0,
      bigBlindIndex: _bigBlindIndex ?? 0,
      smallBlind: _smallBlind,
      bigBlind: _bigBlind,
      burnedCards: burnedCards.map((c) => _cardString(c)).toList(),
      boardCards: communityCards.map((c) => _cardString(c)).toList(),
      actionHistory: List<String>.from(_handHistory),
      streetActions: List.from(_streetActions),
      mainPot: _displayedPot,
      sidePots: _sidePots
          .map((sp) => {
                "amount": sp.amount,
                "eligibles": sp.eligibles,
              })
          .toList(),
      winnerSeats: _potWinnerSeats.expand((list) => list).toList(),
      finalHandDescriptions: Map<int, String>.from(_finalDescriptions),
      showdownCards: Map<int, List<String>>.from(_showdownMap),
      seatsSawFlop: List<int>.from(_seatsSawFlop),
      seatsSawTurn: List<int>.from(_seatsSawTurn),
      seatsSawRiver: List<int>.from(_seatsSawRiver),
      solverData: {},
    );
    final box = Hive.box<HandRecord>('hand_records');
    box.add(record);

    // Reset for next hand
    _streetActions = [];
    _seatsSawFlop = [];
    _seatsSawTurn = [];
    _seatsSawRiver = [];
    _finalDescriptions = {};
    _showdownMap = {};
  }

  String _cardString(CardModel c) {
    String suitSymbol;
    switch (c.suit) {
      case "Hearts":
        suitSymbol = "♥";
        break;
      case "Diamonds":
        suitSymbol = "♦";
        break;
      case "Clubs":
        suitSymbol = "♣";
        break;
      case "Spades":
        suitSymbol = "♠";
        break;
      default:
        suitSymbol = "?";
        break;
    }
    return "${c.rank}$suitSymbol";
  }

  Future<void> _runCalculateAllThenContinue() async {
    // If seat != 0, do AI if needed:
    // if (_currentTurnIndex != null && _currentTurnIndex != 0) {
    //   _aiAction();
    // }
  }

  final int seatCount = 9;
  List<Widget> _buildSeats(double parentWidth, double parentHeight) {
    final cx = parentWidth / 2;
    final cy = parentHeight / 2;
    final rx = _seatCircleRx;
    final ry = _seatCircleRy;

    final seats = <Widget>[];
    for (int i = 0; i < seatCount; i++) {
      final angle = (math.pi / 2) + i * (2 * math.pi / seatCount);
      final sx = cx + rx * math.cos(angle);
      final sy = cy + ry * math.sin(angle);

      seats.add(
        Positioned(
          left: sx - 22,
          top: sy - 22,
          child: Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color.fromARGB(255, 67, 68, 68),
              boxShadow: [
                // Add a subtle shadow behind each seat
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  offset: const Offset(0, 3),
                  blurRadius: 6,
                ),
              ],
            ),
            alignment: Alignment.center,
            child: Text(
              // Seat number (1-based)
              '',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
        ),
      );
    }

    return seats;
  }

  // ================
  // UI
  // ================
  @override
  Widget build(BuildContext context) {
    final bool isMobile = MediaQuery.of(context).size.width < 600;
    final double fullW = MediaQuery.of(context).size.width;
    final double scaleFactor = fullW / 1920;
    final storedTableId = html.window.localStorage['tableId'];
    final storedSeatStr = html.window.localStorage['seatPosition'];
    //final isCurrentTurn = (seatIndex == _currentTurnIndex);
    final bool timerAllowed = _serverTurnDurationMs != 0 &&
        //isCurrentTurn &&
        !_isDealing &&
        !(gameStage == GameStage.flop && !communityCards[0].faceUp) &&
        !(gameStage == GameStage.turn && !communityCards[3].faceUp) &&
        !(gameStage == GameStage.river && !communityCards[4].faceUp) &&
        gameStage != GameStage.showdown &&
        gameStage != GameStage.payout;
    final playerDataLoading = !_dataLoaded;
    final needsToJoin = (storedTableId == null ||
        storedSeatStr == null ||
        storedTableId != widget.roomId);

    // If we’re either loading data or missing local details,
    // show a single Scaffold with different text depending on the cause.
    if (playerDataLoading || needsToJoin) {
      return Scaffold(
        //backgroundColor: Colors.grey.shade200,
        body: Stack(
          children: [
            // Dark background fill
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  color: Color.fromARGB(255, 0, 33, 35),
                  image: DecorationImage(
                    image: AssetImage('assets/images/bg.png'),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),

            // Center “table” background using CustomPaint
            Align(
              alignment: Alignment.center,
              child: SizedBox(
                width: 700,
                height: 500,
                child: LayoutBuilder(
                  builder: (ctx, constraints) {
                    return Stack(
                      clipBehavior: Clip.none,
                      children: [
                        // The poker table itself
                        Image.asset(
                          'assets/images/table.png',
                          width: 700,
                          height: 500,
                          fit: BoxFit.contain,
                        ),

                        // Conditional text
                        Positioned(
                          top: 225,
                          // On mobile, make it span the whole width so we can center the text
                          left: isMobile ? 0 : null,
                          right: isMobile ? 0 : 250,
                          child: Container(
                            // Only size the container on mobile
                            width: isMobile ? fullW : null,
                            alignment: isMobile
                                ? Alignment.center
                                : Alignment.centerRight,
                            child: Text(
                              playerDataLoading
                                  ? "Loading Players From Server..."
                                  : "Please join the table to play",
                              textAlign:
                                  isMobile ? TextAlign.center : TextAlign.left,
                              style: const TextStyle(
                                color: Color.fromARGB(255, 63, 63, 63),
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),

                        // Seats placed in a circle around the table
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade200,
      body: Column(
        children: [
          // Table / Board / Players
          Expanded(
            child: LayoutBuilder(
              builder: (ctx, constraints) {
                final fullW = constraints.maxWidth;
                final fullH = constraints.maxHeight;
                final isPortrait = fullH > fullW;
                // Center coords for seat math
                final cx = fullW / 2;
                final cy = fullH / 2;

                const double _potLabelAreaHeight = 48.0;
                // How wide the history panel should be when centered
                final historyWidth = isPortrait ? fullW * 0.9 : 300.0;
                return Stack(
                  //alignment: Alignment.center,
                  children: [
                    // 1) Background fill
                    Positioned.fill(
                      child: Container(
                        decoration: const BoxDecoration(
                          color: Color.fromARGB(255, 7, 32, 30),
                          image: DecorationImage(
                              image: AssetImage('assets/images/bg.png'),
                              fit: BoxFit.cover,
                              opacity: .05),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 120,
                      left: 10,
                      child: ActionLogModal(
                        panelState: _historyPanelState,
                        onPanelToggle: _toggleHistory,
                        handHistory: _handHistory,
                        historyScrollController: _historyScrollController,
                        onCopyHistory: _copyHistory,
                        onSwapCardView: _toggleCardView,
                        onExitTable: _onExitTablePressed,
                      ),
                    ),
                    // 2) The main table area (700x500) in the center

                    Align(
                        alignment: Alignment.topCenter,
                        child: CustomMenu(
                            showCountdown: _isGameStarted && timerAllowed,
                            countDown: _serverTurnDurationMs != 0
                                ? ' ${(_serverTurnDurationMs / 1000 + -(_serverTurnDurationMs / 1000 * _turnTimerController.value).round()).toString()}'
                                : _countdownText)),
                    if (_isGameStarted && !isMobile)
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.blueGrey.shade900,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            child: ElevatedButton(
                              onPressed: _toggleCardView,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                shadowColor: Colors.transparent,
                              ).copyWith(
                                overlayColor:
                                    MaterialStateProperty.resolveWith<Color?>(
                                        (states) {
                                  if (states.contains(MaterialState.pressed) ||
                                      states.contains(MaterialState.hovered)) {
                                    return const Color.fromRGBO(3, 192, 193, 1)
                                        .withOpacity(0.3);
                                  }
                                  return null;
                                }),
                              ),
                              child: const Text(
                                "Swap Card View",
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ),
                      ),

                    if (_isGameStarted && _myServerSeatIndex != null) ...[
                      Align(
                        alignment: Alignment.topRight,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  const Color.fromARGB(255, 226, 86, 86),
                              foregroundColor: Colors.white,
                              shape: const CircleBorder(),
                              padding: const EdgeInsets.all(12),
                            ),
                            onPressed: _onExitTablePressed,
                            // ← explicitly set the icon color:
                            child: const Icon(
                              Icons.logout,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],

                    // =========================
                    // PLAYER SEATS
                    // =========================
                    // ..._buildPlayerSeats(
                    //     parentWidth: fullW,
                    //     parentHeight: fullH,
                    //     isMobile: isMobile),

                    // =========================
                    // DEALING ANIMATION
                    // =========================
                    if (_isDealing)
                      Positioned(
                        left: _flyingCardOffset.dx,
                        top: _flyingCardOffset.dy,
                        child: SizedBox(
                          width: _scaledSmallCardWidth,
                          height: _scaledSmallCardHeight,
                          child: _buildCardBack(),
                        ),
                      ),

                    // =========================
                    // COLLECTING BETS
                    // =========================
                    if (_isCollectingBets)
                      ...List.generate(players.length, (i) {
                        final bet = _pendingBets[i];
                        if (bet <= 0) return const SizedBox.shrink();
                        final t = _chipCollectController.value;
                        final seatPos = _chipOffsets[i];
                        final centerPos = _tableCenterOffset();
                        final adjustedCenter =
                            Offset(centerPos.dx - 30, centerPos.dy + 25);

                        final dx =
                            seatPos.dx + t * (adjustedCenter.dx - seatPos.dx);
                        final dy =
                            seatPos.dy + t * (adjustedCenter.dy - seatPos.dy);

                        return Positioned(
                          left: dx,
                          top: dy,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Image.asset(
                                "assets/images/chips.png",
                                width: 32,
                                height: 32,
                                fit: BoxFit.cover,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                "\$$bet",
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                      
                    // =========================
                    // BOTTOM PLAYER CONTROLS
                    // =========================


                    // =========================
                    // PAYOUT ANIMATION
                    // =========================
                    ..._buildPayoutAnimationWidgets(),
                    Align(
                      alignment: Alignment.center,
                      child: Transform.scale(
                        scale: scaleFactor,
                        child: SizedBox(
                        width: 700,
                        height: 500,
                        child: LayoutBuilder(
                          builder: (ctx, constraints) {
                            final fullW = constraints.maxWidth;
                            final fullH = constraints.maxHeight;
                            final isPortrait = fullH > fullW;
                            return Transform.scale(
                                scale: 1 ,
                                child:
                                    Stack(clipBehavior: Clip.none, children: [
                                  Container(color: Colors.transparent),
                                  ..._buildPlayerSeats(
                                      parentWidth: fullW,
                                      parentHeight: fullH,
                                      isMobile: isMobile),
                                  Image.asset('assets/images/table.png',
                                      width: 700 * 1,
                                      height: 500,
                                      fit: BoxFit.contain,
                                      colorBlendMode: BlendMode.srcOver,
                                      color: Colors.transparent),
                    Align(
                      alignment: Alignment.center,
                      child: SizedBox(
                        width: 600 ,
                        height: 500,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // The countdown text if we're showing it
                            // if (_showCountdown) ...[
                            //   Positioned(
                            //     top: 145,
                            //     child: Container(
                            //       padding: const EdgeInsets.all(10),
                            //       child: Text(
                            //         _countdownText,
                            //         style: const TextStyle(
                            //           fontSize: 20,
                            //           color: Color.fromARGB(255, 63, 63, 63),
                            //           fontWeight: FontWeight.bold,
                            //         ),
                            //       ),
                            //     ),
                            //   ),
                            // ],

                            // =========================
                            // Show different UI if game not started yet
                            // =========================
                            // 2) Pre‑game: share link + start button
                            if (!_isGameStarted) ...[
                              if (iAmHost && _anySeatOpen())
                                Positioned(
                                  // slightly lower so you can see the top‐center table
                                  top: 160,
                                  child: Container(
                                    width: isPortrait
                                        ? fullW * 0.6
                                        : 450, // narrower on mobile
                                    height: isPortrait ? 70 : 75,
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF3F3F3F),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Text(
                                          "Share this link to join the room:",
                                          style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.white),
                                        ),
                                        const SizedBox(height: 6),
                                        GestureDetector(
                                          onTap: () {
                                            final String baseUrl =
                                                Uri.base.origin;
                                            final String link =
                                                '$baseUrl/game/$_roomId/join';
                                            Clipboard.setData(
                                                ClipboardData(text: link));
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              SnackBar(
                                                  content: Text(
                                                      "Link copied: $link")),
                                            );
                                          },
                                          child: MouseRegion(
                                            cursor: SystemMouseCursors.click,
                                            child: ScrollbarTheme(
                                              data: ScrollbarThemeData(
                                                thumbColor:
                                                    WidgetStateProperty.all(
                                                        Colors.grey
                                                            .withOpacity(0.5)),
                                                trackColor:
                                                    WidgetStateProperty.all(
                                                        Colors.grey
                                                            .withOpacity(0.2)),
                                                thickness:
                                                    WidgetStateProperty.all(
                                                        4), // slim scrollbar
                                                radius: const Radius.circular(
                                                    3), // rounded corners
                                              ),
                                              child: Scrollbar(
                                                controller:
                                                    _scrollCtrl, // <-- Add this
                                                thumbVisibility: true,
                                                trackVisibility: true,
                                                child: SingleChildScrollView(
                                                  controller:
                                                      _scrollCtrl, // <-- And this
                                                  scrollDirection:
                                                      Axis.horizontal,
                                                  child: Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 8,
                                                        vertical: 4),
                                                    decoration: BoxDecoration(
                                                      color: Colors.grey[300],
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              4),
                                                    ),
                                                    child: Text(
                                                      '${Uri.base.origin}/game/$_roomId/join',
                                                      style: const TextStyle(
                                                        fontSize: 11,
                                                        color:
                                                            Colors.blueAccent,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              if (iAmHost)
                                Positioned(
                                  top: 275,
                                  child: ElevatedButton(
                                    onPressed: _onStartGamePressed,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF3F3F3F),
                                      foregroundColor: Colors.white,
                                    ),
                                    child: const Text("Start Game"),
                                  ),
                                )
                              else
                                // non‐hosts see the “waiting” message instead of the button
                                Positioned(
                                  top: isPortrait ? 220 : 255,
                                  child: Container(
                                    padding: const EdgeInsets.all(10),
                                    color: Colors.black54,
                                    child: const Text(
                                      "Waiting for host to start game…",
                                      style: TextStyle(
                                          fontSize: 16, color: Colors.white),
                                    ),
                                  ),
                                ),
                            ],

                            // =========================
                            // If _isGameStarted == true, show the table + cards
                            // =========================
                            // ===== In‑game: burn + community + pot labels =====
                            if (_isGameStarted)
                              Align(
                                alignment: Alignment.center,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Burned cards above / beside community cards
                                    Builder(builder: (ctx) {
                                      final narrow =
                                          MediaQuery.of(ctx).size.width < 600;
                                      if (narrow) {
                                        return Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            // add 5px left padding
                                            Center(
                                              child: Transform.translate(
                                                offset: const Offset(0, 0),
                                                child: _buildBurnedCards(),
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: List.generate(
                                                5,
                                                (i) => _buildCommunityCard(i),
                                              ),
                                            ),
                                          ],
                                        );
                                      } else {
                                        // DESKTOP/TABLET: Row
                                        return Row(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          mainAxisAlignment:  MainAxisAlignment.center,
                                          children: [
                                            _buildBurnedCards(),
                                            SizedBox(width: 14),
                                            Row(
                                              mainAxisSize: MainAxisSize.max,
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: List.generate(
                                                5,
                                                (i) => _buildCommunityCard(i),
                                              ),
                                            ),
                                          ],
                                        );
                                      }
                                    }),

                                    const SizedBox(height: 8),

                                    SizedBox(
                                      height: _potLabelAreaHeight,
                                      child: _sidePots.isEmpty
                                          ? Center(
                                              child: Container(
                                                  width: 150,
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 12,
                                                      vertical: 8),
                                                  decoration: BoxDecoration(
                                                    color: Colors.black
                                                        .withOpacity(0.5),
                                                    border: Border.all(
                                                        color: Colors.black,
                                                        width: 1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            30),
                                                  ),
                                                  child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Stack(
                                                            alignment: Alignment
                                                                .center,
                                                            children: [
                                                              Text(
                                                                "Pot: ",
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  foreground:
                                                                      Paint()
                                                                        ..style =
                                                                            PaintingStyle.stroke
                                                                        ..strokeWidth =
                                                                            4
                                                                        ..color =
                                                                            Colors.black,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              ),
                                                              Text(
                                                                "Pot: ",
                                                                style:
                                                                    const TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              )
                                                            ]),
                                                        SizedBox(
                                                          width: 4,
                                                        ),
                                                        Stack(
                                                            alignment: Alignment
                                                                .center,
                                                            children: [
                                                              Text(
                                                                "\$$pot",
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  foreground:
                                                                      Paint()
                                                                        ..style =
                                                                            PaintingStyle.stroke
                                                                        ..strokeWidth =
                                                                            4
                                                                        ..color =
                                                                            Colors.black,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              ),
                                                              Text(
                                                                "\$$pot",
                                                                style:
                                                                    const TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              )
                                                            ])
                                                      ])),
                                            )
                                          : Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: List.generate(
                                                  _sidePots.length, (i) {
                                                final pot = _sidePots[i];
                                                // hide while animating payouts
                                                final hiding = gameStage ==
                                                        GameStage.payout &&
                                                    (i == _payoutAnimatingPotIndex ||
                                                        i < _payoutAnimatingPotIndex);

                                                // ensure we have a key
                                                if (_potLabelKeys.length <= i) {
                                                  _potLabelKeys.add(GlobalKey<
                                                      _PotLabelState>());
                                                }
                                                final key = _potLabelKeys[i];

                                                // First, only include non-folded seats
                                                final eligibleSeats = pot
                                                    .eligibles
                                                    .where((serverIdx) =>
                                                        !players[serverIdx]
                                                            .isFolded)
                                                    .toList();

                                                // Then map to names (or fallback to seat number)
                                                final eligibleNames =
                                                    eligibleSeats
                                                        .map((serverIdx) {
                                                  final name =
                                                      players[serverIdx].name;
                                                  if (name.isNotEmpty)
                                                    return name;
                                                  final local =
                                                      serverToLocal(serverIdx);
                                                  return 'Seat ${local + 1}';
                                                }).join(', ');

                                                final bool isPayoutPhase =
                                                    gameStage ==
                                                            GameStage
                                                                .showdown ||
                                                        gameStage ==
                                                            GameStage.payout;

                                                // Only show “Winner:” if exactly one seat remains eligible
                                                final tooltipText = (isPayoutPhase &&
                                                        eligibleSeats.length ==
                                                            1)
                                                    ? 'Winner: $eligibleNames'
                                                    : 'Eligible: $eligibleNames';

                                                return Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 10,
                                                      vertical: 4),
                                                  child: Visibility(
                                                    visible: !hiding,
                                                    maintainSize: true,
                                                    maintainAnimation: true,
                                                    maintainState: true,
                                                    child: Tooltip(
                                                      message: tooltipText,
                                                      child: PotLabel(
                                                        key: key,
                                                        potIndex: i,
                                                        label:
                                                            "Pot ${i + 1}: \$${pot.amount}",
                                                        onMeasured:
                                                            (pIdx, pos) {
                                                          _potLabelOffsets[
                                                                  pIdx] =
                                                              Offset(pos.dx,
                                                                  pos.dy);
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              }),
                                            ),
                                    ),
                                  ],
                                ),
                              ),
                         
                          ],
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.center,
                      child: SizedBox(
                        width: 700,
                        height: 500,
                        child: LayoutBuilder(
                          builder: (ctx, constraints) {
                            final fullW = constraints.maxWidth;
                            final fullH = constraints.maxHeight;
                            final isPortrait = fullH > fullW;
                            return Transform.scale(
                                scale: 1 ,
                                child:
                                    Stack(clipBehavior: Clip.none, children: [
                                  ..._buildPlayerSeats(
                                      parentWidth: fullW,
                                      parentHeight: fullH,
                                      isMobile: isMobile,
                                      empty: false),
                                ]));
                          },
                        ),
                      ),
                    ),

                
                                ]));
                          },
                        ),
                      )
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Padding(
                          padding: const EdgeInsets.only(bottom: 50),
                          child: Opacity(
                            opacity: 1,
                            child: _buildPlayerControls(isMobile: isMobile),
                          )),
                    ),

                    // Align(
                    //   alignment: Alignment.bottomRight,
                    //   child: WinningsDisplay()),
                    //           // ..._buildPlayerSeats(
                    //           //     parentWidth: fullW,
                    //           //     parentHeight: fullH,
                    //           //     isMobile: isMobile,
                    //           //     empty: false),
                    //           PlayingCardWidget(rank: '7', suit: '♥'),
                    // Center( child: Text(_countdownTimer.toString())),
                    // Center( child: Text((scaleFactor).toString())),
  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildPlayerSeats({
    required double parentWidth,
    required double parentHeight,
    required bool isMobile,
    final empty = true,
  }) {
    final seatCount = players.length;
    final mySeat = _myServerSeatIndex ?? 0;
    final seatWidgets = <Widget>[];

    // Center of the table
    final cx = parentWidth / 2;
    final cy = parentHeight / 2;

    // Radii for ellipse
    final rx = _seatCircleRx;
    final ry = _seatCircleRy;

    for (int offset = 0; offset < seatCount; offset++) {
      // "Real" seat index rotated so mySeat is at offset=0
      final realSeat = (mySeat + offset) % seatCount;

      // Compute angle and base position
      final angle = (math.pi / 2) + offset * (2 * math.pi / seatCount);
      final baseX = cx + rx * math.cos(angle);
      final baseY = cy + ry * math.sin(angle);

      // Initialize both circle and tab positions to base
      var sxCircle = baseX;
      var syCircle = baseY;
      var sxTab = baseX;
      var syTab = baseY;

      // Apply mobile-specific overrides if provided
      if (isMobile && _mobileSeatPositionOffsets.containsKey(offset)) {
        final o = _mobileSeatPositionOffsets[offset]!;
        sxCircle += o.circle.dx;
        syCircle += o.circle.dy;
        sxTab += o.tab.dx;
        syTab += o.tab.dy;
      }

      if (seatActive[realSeat] && !empty) {
        
        final isMain = realSeat == mySeat;
        print(
            'sxTab:   ${sxTab - (isMain ? 70 : 60) - 70}  parentWidth: ${parentWidth / 2}');
        // 1) Player tab at shifted position
        seatWidgets.add(
          Positioned(
            left: sxTab - 125,
            top: isMain ? syTab - 175 : syTab - 100,
            child: _playerTab(players[realSeat], isMain, realSeat, mySeat),
          ),
        );

        // 2) Bet stack positioning if needed
        final betAmt = _amountsOnTable[realSeat];
        final showSeatBetStack = !_isCollectingBets &&
            gameStage != GameStage.showdown &&
            gameStage != GameStage.payout &&
            betAmt > 0;

        if (showSeatBetStack) {
          const fraction = 0.42;
          final betX = sxCircle + fraction * (cx - sxCircle) - 35;
          final betY = syCircle + fraction * (cy - syCircle) - 10;

          // seatWidgets.add(
          //   Positioned(
          //     left: isMain ? 100 : 0 ,
          //     top: isMain? 100 : 0,
          //     child: _buildBetStack(betAmt),
          //   ),
          // );
        }
      }
      if (empty && !seatActive[realSeat]) {
        // Inactive seat: draw the seat circle with number
        seatWidgets.add(
          Positioned(
            left: sxCircle - 100,
            top: syCircle - 65,
            child: Transform.rotate(
                angle: ![4, 5].contains(offset) ? angle + math.pi / 2 : 0,
                //angle:0,
                child: Column(children: [
                  //   Text('myseat: $mySeat  realSeat: $realSeat  offset: $offset'),
                  Image.asset(
                    'assets/images/chair.png',
                    width: 200,
                    height: 154,
                    fit: BoxFit.scaleDown,
                  )
                ])),
          ),
        );
      }
    }

    return seatWidgets;
  }

  Widget _buildBetStack(int betAmt) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(
          "assets/images/chips.png",
          width: 32,
          height: 32,
          fit: BoxFit.cover,
        ),
        const SizedBox(width: 4),
        Stack(children: [
          Text(
            "\$$betAmt",
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w900,
              foreground: Paint()
                ..style = PaintingStyle.stroke
                ..strokeWidth = 4
                ..color = Colors.black,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            "\$$betAmt",
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w900,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          )
        ]),
      ],
    );
  }

  Widget _playerTab(Player p, bool isMain, int seatIndex, mySeat) {
    // Determine if we're on mobile (screen width < 600)
    final isMobile = MediaQuery.of(context).size.width < 600;
    // Scale down by 80% (i.e. −20%) on mobile
    print(
        "isMobile: $isMobile  isMain: $isMain  seatIndex: $seatIndex  gameStage: $gameStage player: ${p.name}");
    final scale = isMobile ? (isMain ? 0.8 : 0.55) : 1.0;

    // Original tab dimensions and configuration
    const double tabW = 120.0;
    const double tabH = 160.0;
    const double avatarR = 25.0;
    const double avatarD = avatarR * 2;
    const double nameH = 30;
    const double actionH = 30;
    String seatPos = '';
    final folded = players[seatIndex].isFolded;
    final allIn = players[seatIndex].isAllIn;
    final seatChips = players[seatIndex].chips.toLowerCase();
    double finalOpacity = folded ? 0.4 : 1.0;
    final isCurrentTurn = (seatIndex == _currentTurnIndex);
    final isWinner = _playerActions[seatIndex].contains("Win:") ||
        _playerActions[seatIndex].contains("Chop:");
    final bool eliminated = p.eliminated;
    String actionLabel = _playerActions[seatIndex];

    if (eliminated) {
      finalOpacity = 0.4;
      actionLabel = "Eliminated";
    } else if (folded) {
      finalOpacity = 0.4;
    }

    // Position tokens
    final tokens = <Widget>[];
    if (seatIndex == _dealerIndex) {
      tokens.add(_buildPosToken("D"));
      seatPos = "D";
    }
    if (seatIndex == _smallBlindIndex) {
      tokens.add(_buildPosToken("SB"));
      seatPos = "SB";
    }
    if (seatIndex == _bigBlindIndex) {
      tokens.add(_buildPosToken("BB"));
      seatPos = "BB";
    }

    // Decide avatar color
    Color avatarColor = Colors.grey.shade400;
    if (isMain) {
      avatarColor = const Color.fromARGB(255, 4, 159, 206);
    }
    if (isCurrentTurn) {
      avatarColor = const Color.fromRGBO(6, 223, 140, 1);
    }
    if (isWinner) {
      avatarColor = Colors.amber.shade500;
    }

    final bool timerAllowed = _serverTurnDurationMs != 0 &&
        isCurrentTurn &&
        !_isDealing &&
        !(gameStage == GameStage.flop && !communityCards[0].faceUp) &&
        !(gameStage == GameStage.turn && !communityCards[3].faceUp) &&
        !(gameStage == GameStage.river && !communityCards[4].faceUp) &&
        gameStage != GameStage.showdown &&
        gameStage != GameStage.payout;

    // Wrap the original tab in a Transform.scale for mobile resizing
    return Transform.scale(
      scale: 1,
      alignment: Alignment.center,
      child: Opacity(
        opacity: finalOpacity,
        child: Container(
          // width: tabW,
          // height: tabH,
          decoration: BoxDecoration(
            //  color: isWinner ? Colors.amber.shade500 : Colors.blueGrey.shade800,
            borderRadius: BorderRadius.circular(8),
          ),
          child: isMain
              ? PlayerComponent(
                lastMove : _playerActions[seatIndex],
                  player: p,
                  seatPosition: seatPos,
                  amountOnTable: _buildBetStack(_amountsOnTable[seatIndex]),
                  turnTimerController: _turnTimerController,
                  isCurrentTurn: isCurrentTurn,
                  mySeat: mySeat,
                  seatIndex: seatIndex,
                  numPlayers:  _countActiveSeats())
              : VillianComponent(
                 lastMove : _playerActions[seatIndex],
                  player: p,
                  seatPosition: seatPos,
                  amountOnTable: _buildBetStack(_amountsOnTable[seatIndex]),
                  turnTimerController: _turnTimerController,
                  isCurrentTurn: isCurrentTurn,
                  mySeat: mySeat,
                  seatIndex: seatIndex,
                  numPlayers:  _countActiveSeats()
                  ),
        ),
      ),
    );
  }

  void _confirmRemovePlayer(String playerName, int seatIndex) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) {
        return Dialog(
          backgroundColor: const Color(0xFF2E2D2D),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: Container(
            width: 340, // ← same width as your other modals
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFF2E2D2D),
              borderRadius: BorderRadius.circular(8),
              boxShadow: const [
                BoxShadow(
                    color: Colors.black26,
                    offset: Offset(0, 4),
                    blurRadius: 10),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  "Remove Player?",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color.fromARGB(255, 4, 159, 206),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  "Do you want to remove $playerName from the table?",
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () => Navigator.of(ctx).pop(),
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey),
                      child: const Text(
                        "No",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(ctx).pop();
                        _removePlayer(seatIndex);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color.fromARGB(255, 4, 159, 206),
                      ),
                      child: const Text(
                        "Yes",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _removePlayer(int seatIndex) {
    // locally hide them immediately if you want:
    setState(() => seatActive[seatIndex] = false);
    // tell server to remove
    socket.emit("leaveSeat", {
      "roomId": _roomId,
      "seatIndex": seatIndex,
    });
  }

  Widget _buildPosToken(String lbl) {
    return Container(
      width: 28,
      height: 28,
      margin: const EdgeInsets.only(top: 4),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.orange.shade800.withOpacity(0.9),
        border: Border.all(color: Colors.black),
      ),
      alignment: Alignment.center,
      child: Text(
        lbl,
        style: const TextStyle(
            color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildHoleCardRow(List<CardModel> cards, bool isMain,
      {required int seatIndex}) {
    // final bool isLastPlayerLeft = players.where((p) => !p.isFolded).length == 1;
    // final bool revealSelfOnly = seatIndex == _myServerSeatIndex;

    // Decide if a given card index should be face‑up:
    bool shouldReveal(int cardIndex) {
      // 1) Never reveal cards that haven’t been dealt
      if (cards.length <= cardIndex) return false;

      // 2) Count how many active, non‑folded players remain
      int remaining = 0;
      for (int i = 0; i < players.length; i++) {
        if (seatActive[i] && !players[i].isFolded) {
          remaining++;
        }
      }

      // 3) If only one is left, reveal only to that seat’s owner
      final isSelf = (seatIndex == _myServerSeatIndex);
      if (remaining == 1) {
        return isSelf;
      }

      // 4) Otherwise (multi‑way showdown), once both cards are dealt:
      if (cards.length >= 2) {
        // show everyone’s cards if showAllCards==true
        if (showAllCards && !players[seatIndex].isFolded) return true;
        // else only reveal your own
        return isSelf;
      }

      // 5) Pre‑flop or one card dealt: still only you see your own
      return isSelf;
    }

    // Helper to build either the real card or an empty placeholder/back
    Widget buildSlot(int idx) {
      if (cards.length > idx) {
        // we have a cardModel, so draw it either faceUp or faceDown
        return _buildCardContainer(cards[idx], shouldReveal(idx));
      } else {
        // no card dealt yet: show blank back (or placeholder)
        return _buildCardContainer(null, false);
      }
    }

    return Row(
      children: [
        buildSlot(0),
        const SizedBox(width: 0),
        buildSlot(1),
      ],
    );
  }

  Widget _buildCardContainer(CardModel? c, bool faceUp) {
    if (c == null) {
      // empty placeholder
      return Container(
        width: 60,
        height: 75,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.2),
          borderRadius: BorderRadius.circular(4),
        ),
      );
    }
    final isEmpty = c.rank.isEmpty && c.suit.isEmpty;
    // Use scaled dimensions for player cards
    final cardWidth = _scaledSmallCardWidth * 1.2;
    final cardHeight = _scaledSmallCardHeight * 1.07;

    if (isEmpty) {
      // Interpret empty rank/suit as a face‑down card
      return _buildCardBack(width: cardWidth, height: cardHeight);
    }
    if (!faceUp) {
      // Show card back
      return _buildCardBack(width: cardWidth, height: cardHeight);
    }
    // Otherwise show card front
    return _buildCardFront(c, width: cardWidth, height: cardHeight);
  }

  Widget _buildBurnedCards() {
    final isMobile = MediaQuery.of(context).size.width < 600;
    // Use scaled dimensions based on screen width
    final double cardWidth =
        isMobile ? _scaledSmallCardWidth * 0.6 : _scaledCardWidth;
    final double cardHeight =
        isMobile ? _scaledSmallCardHeight * 0.64 : _scaledCardHeight;
    final double overlap =
        isMobile ? _scaledSmallCardWidth * 0.3 : _scaledCardWidth * 0.5;

    return SizedBox(
      width: cardWidth + 2 * overlap,
      height: cardHeight,
      child: Stack(
        children: List.generate(3, (i) {
          final bc = burnedCards[i];
          final empty = (bc.rank.isEmpty || bc.suit.isEmpty);
          final faceUp = showAllCards;

          // Convert suit names to symbols for PlayingCardWidget
          final suitSymbol = (bc.suit == "Hearts")
              ? "♥"
              : (bc.suit == "Diamonds")
                  ? "♦"
                  : (bc.suit == "Clubs")
                      ? "♣"
                      : (bc.suit == "Spades")
                          ? "♠"
                          : "?";

          return Positioned(
              left: i * overlap,
              child: empty
                  ? Container(
                      width: cardWidth,
                      height: cardHeight,
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.0),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.transparent),
                      ),
                    )
                  : faceUp
                      ? PlayingCardWidget.burned(
                          rank: bc.rank,
                          suit: suitSymbol,
                          screenWidth: MediaQuery.of(context).size.width,
                        )
                      : Container(
                          width: cardWidth,
                          height: cardHeight,
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 120),
                            borderRadius:
                                BorderRadius.circular(12 * _screenScaleFactor),
                            // border: Border.all(
                            //     color: Colors.transparent, width: .7),
                          ),
                          child: _buildCardBack(
                              width: cardWidth, height: cardHeight),
                        ));
        }),
      ),
    );
  }

  Widget _buildCommunityCard(int i) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final double hMargin =
        isMobile ? _screenScaleFactor : _screenScaleFactor * 2.0;

    // Use scaled dimensions
    final double cardWidth =
        isMobile ? _scaledSmallCardWidth * 0.6 : _scaledCardWidth;
    final double cardHeight =
        isMobile ? _scaledSmallCardHeight * 0.64 : _scaledCardHeight;

    final c = communityCards[i];
    final empty = (c.rank.isEmpty || c.suit.isEmpty);
    final faceUp = c.faceUp;

    // Convert suit names to symbols for PlayingCardWidget
    final suitSymbol = (c.suit == "Hearts")
        ? "♥"
        : (c.suit == "Diamonds")
            ? "♦"
            : (c.suit == "Clubs")
                ? "♣"
                : (c.suit == "Spades")
                    ? "♠"
                    : "?";

    return Container(
      margin: EdgeInsets.fromLTRB(
          0, 0, i == communityCards.length - 1 ? 0 : hMargin * 4, 0),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12 * _screenScaleFactor),
        border: Border.all(color: Colors.transparent, width: .00001),
                    boxShadow: [
              BoxShadow(
                color: Colors.transparent,
                blurRadius: 0,
                offset: const Offset(0, 0),
              ),
            ],
      ),
      child: empty
          ? Container(
              width: cardWidth,
              height: cardHeight,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(12 * _screenScaleFactor),
                border: Border.all(
                    color: Colors.transparent,
                    width: 0),
              ),
            )
          : faceUp
              ? PlayingCardWidget.community(
                  rank: c.rank,
                  suit: suitSymbol,
                  isMobile: isMobile,
                  width: cardWidth,
                  height: cardHeight,
                )
              : _buildCardBack(
                  width: cardWidth,
                  height: cardHeight,
                ),
    );
  }

  bool _swapCardView = false;

  // Toggles the card view style.
  void _toggleCardView() {
    setState(() {
      _swapCardView = !_swapCardView;
    });
  }

  BoxDecoration _buildSwappedCardDecoration(String suit) {
    Color baseColor;
    switch (suit) {
      case "Clubs":
        baseColor = Colors.green;
        break;
      case "Hearts":
        baseColor = Colors.red;
        break;
      case "Spades":
        baseColor = Colors.grey;
        break;
      case "Diamonds":
        baseColor = Colors.blue;
        break;
      default:
        baseColor = Colors.black;
        break;
    }

    // Compute a lighter tint for the base color using HSL conversion.
    final HSLColor hslBase = HSLColor.fromColor(baseColor);
    final double lightIncrement = 0.3;
    // Ensure the lightness does not exceed 1.0.
    final double newLightness =
        (hslBase.lightness + lightIncrement).clamp(0.0, 1.0);
    final Color lighterColor = hslBase.withLightness(newLightness).toColor();

    return BoxDecoration(
      borderRadius: BorderRadius.circular(4),
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          lighterColor, // Lighter tint at the top.
          baseColor, // Primary color at the bottom.
        ],
        stops: [0.0, 0.5],
      ),
    );
  }

  Widget _buildCardFront(
    CardModel c, {
    double width = 80,
    double height = 120,
    bool isCommunityCard = false,
  }) {
    final suit = c.suit;
    final rank = c.rank;
    final sym = (suit == "Hearts")
        ? "♥"
        : (suit == "Diamonds")
            ? "♦"
            : (suit == "Clubs")
                ? "♣"
                : (suit == "Spades")
                    ? "♠"
                    : "?";

    // Detect mobile
    final bool isMobile = MediaQuery.of(context).size.width < 600;

    // Font sizes: smaller when mobile AND community/burned cards
    final double rankFontSize =
        isCommunityCard ? (isMobile ? 14.0 : 80.0) : (isMobile ? 22.0 : 80.0);
    final double suitFontSize =
        isCommunityCard ? (isMobile ? 16.0 : 120.0) : (isMobile ? 24.0 : 120.0);

    // If you're in the swapped‑card view:
    if (_swapCardView) {
      Text("SWAPCARD");
    }

    // Default card‑face style
    final Color color =
        (suit == "Hearts" || suit == "Diamonds") ? Colors.red : Colors.black;
    return CustomPaint(
      size: Size(width, height),
      painter: PlayingCardPainter(rank: rank, suit: sym, suitColor: color),
    );
  }

  Widget _buildCardBack({double? width, double? height}) {
    // Use scaled dimensions if not provided
    final cardWidth = 60;
    final cardHeight = 90;

    // Scale the border radius and padding based on card size
    final borderRadius = (cardWidth * 0.05).clamp(2.0, 10.0);
    final borderPadding = (cardWidth * 0.25).clamp(1.0, 6.0);

    return  Container(
//margin: EdgeInsets.all(2),
          width: cardWidth as double,
          height: cardHeight as double,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10 * _screenScaleFactor),
            border: Border.all(color: Colors.black, width: .00001),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
              padding: EdgeInsets.all(
                  borderPadding * 1.1), // Creates the white border
              child: Container(
                width: cardWidth - 2 * (borderPadding * 1.2),
                height: cardHeight - 2 * (borderPadding * 1.2),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 41, 151, 214),
                  borderRadius:
                      BorderRadius.circular(12 * _screenScaleFactor / 2),
                ),
                child: Padding(
                  padding: EdgeInsets.all(
                      borderPadding * 1.3 / 4), // Creates the white border
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4 * _screenScaleFactor),
                    child: Container(
                      width: cardWidth - 2 * (borderPadding * 1.8),
                      height: cardHeight - 2 * (borderPadding * 1.3),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 255, 255, 255),
                        borderRadius:
                            BorderRadius.circular(12 * _screenScaleFactor / 2),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(0), // Creates the white border
                        child: ClipRRect(
                          borderRadius:
                              BorderRadius.circular(6 * _screenScaleFactor),
                          child: Image.asset(
                            'assets/images/Sky.png',
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    // Image.asset(
                    //   'assets/images/Sky.png',
                    //   fit: BoxFit.cover,
                    // ),
                  ),
                ),
              )),
        );
  }

  // =========================
  // PLAYER CONTROLS (seat 0)
  // =========================

  /// Common button styles for user actions
  final ButtonStyle _commonActionButtonStyle = ElevatedButton.styleFrom(
    backgroundColor:
        const Color.fromARGB(255, 247, 247, 247), // grey background
    foregroundColor: Colors.grey[800], // darker grey text
  );

  /// Special style for the fold button
  final ButtonStyle _foldButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: Colors.red,
    foregroundColor: Colors.white,
  );

  Widget _buildPlayerControls({required bool isMobile}) {
    // —————————————————————————————————————————————————————————————
    // Compute control state (unchanged)
    // —————————————————————————————————————————————————————————————
    final seat = _currentTurnIndex ?? 0;
    final userControlled = _playerControls[seat];
    final folded = players[seat].isFolded;
    final allIn = players[seat].isAllIn;
    final st = _parseChips(players[seat].chips);

    final isMyTurn = seatActive[seat] &&
        userControlled &&
        !folded &&
        !allIn &&
        st > 0 &&
        _currentTurnIndex == seat;

    final controlsEnabled = isMyTurn &&
        !_userActionLock &&
        !_isCollectingBets &&
        (gameStage == GameStage.preHand ||
            gameStage == GameStage.flop ||
            gameStage == GameStage.turn ||
            gameStage == GameStage.river);

    final needed = _neededToCall(seat);
    final hasActiveBet = _currentBet > 0;

    // —————————————————————————————————————————————————————————————
    // Button labels
    // —————————————————————————————————————————————————————————————
    final betBtnLabel = (st >= _bigBlind) ? "Bet" : "All‑In";

    // APPLY FONT-SIZE CHANGE HERE
    final double ctrlFontSize = isMobile ? 10.0 : 16.0;

    // —————————————————————————————————————————————————————————————
    // If we're in slider mode, keep exactly as before but apply font size
    // —————————————————————————————————————————————————————————————
    if (_showSlider) {
      if (isMobile) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _onCancelBetOrRaise,
                    style: _commonActionButtonStyle,
                    child: Text(
                      "Cancel",
                      style: TextStyle(fontSize: ctrlFontSize),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: controlsEnabled
                        ? () => _onBetOrRaiseConfirmed(seat)
                        : null,
                    style: _commonActionButtonStyle,
                    child: Text(
                      _sliderIsRaise
                          ? "Raise to: \$${(_sliderValue + players[seat].contributedThisStreet).round()}"
                          : "Bet: \$${_sliderValue.round()}",
                      style: TextStyle(fontSize: ctrlFontSize),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              width: MediaQuery.of(context).size.width,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.blueGrey.shade900,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: _buildBetSliderPanel(),
            ),
            const SizedBox(height: 8),
          ],
        );
      } else {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: _onCancelBetOrRaise,
              style: _commonActionButtonStyle,
              child: Text(
                "Cancel",
                style: TextStyle(fontSize: ctrlFontSize),
              ),
            ),
            const SizedBox(width: 8),
            Container(
              width: 400,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.blueGrey.shade900,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                transitionBuilder: (child, anim) => FadeTransition(
                  opacity: anim,
                  child: child,
                ),
                child: _buildBetSliderPanel(),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed:
                  controlsEnabled ? () => _onBetOrRaiseConfirmed(seat) : null,
              style: _commonActionButtonStyle,
              child: Text(
                _sliderIsRaise
                    ? "Raise To: \$${_sliderValue.round()}"
                    : "Bet: \$${_sliderValue.round()}",
                style: TextStyle(fontSize: ctrlFontSize),
              ),
            ),
          ],
        );
      }
    }

    // —————————————————————————————————————————————————————————————
    // Normal action buttons
    // —————————————————————————————————————————————————————————————
    if (isMobile) {
      return Container(
        width: MediaQuery.of(context).size.width,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.blueGrey.shade900,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(8),
            topRight: Radius.circular(8),
          ),
        ),
        child: Row(
          children: [
            // Check
            Expanded(
              child: ElevatedButton(
                onPressed: (controlsEnabled && needed <= 0)
                    ? () => _onCheck(seat)
                    : null,
                style: _commonActionButtonStyle,
                child: Text(
                  "Check",
                  style: TextStyle(fontSize: ctrlFontSize),
                ),
              ),
            ),
            const SizedBox(width: 8),

            // --- CALL / BET ---
            Expanded(
              child: SizedBox(
                child: ElevatedButton(
                  onPressed: controlsEnabled
                      ? (hasActiveBet
                          ? (needed > 0 ? () => _onCall(seat) : null)
                          : () => _onBetClicked(seat))
                      : null,
                  style: _commonActionButtonStyle,
                  // after
                  child: controlsEnabled && hasActiveBet && needed > 0
                      ? (needed >= st
                          ? Text(
                              "All-in",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: ctrlFontSize,
                                height: 1.0,
                              ),
                            )
                          : Text(
                              "Call\n\$${needed}",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: ctrlFontSize,
                                height: 1.0,
                              ),
                            ))
                      : Text(
                          betBtnLabel,
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: ctrlFontSize),
                        ),
                ),
              ),
            ),
            const SizedBox(width: 8),

            // Raise
            Expanded(
              child: ElevatedButton(
                onPressed: (controlsEnabled && hasActiveBet && needed < st)
                    ? () => _onRaiseClicked(seat)
                    : null,
                style: _commonActionButtonStyle,
                child: Text(
                  "Raise",
                  style: TextStyle(fontSize: ctrlFontSize),
                ),
              ),
            ),
            const SizedBox(width: 8),

            // Fold
            Expanded(
              child: ElevatedButton(
                onPressed: controlsEnabled ? () => _onFold(seat) : null,
                style: _foldButtonStyle,
                child: Text(
                  "Fold",
                  style: TextStyle(fontSize: ctrlFontSize),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        width: 500,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(8),
            topRight: Radius.circular(8),
          ),
        ),
        child: Opacity(
          opacity: isMyTurn ? 1.0 : .5,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // On Check
              ElevatedButton(
                  onPressed: (controlsEnabled && needed <= 0)
                      ? () => _onCheck(seat)
                      : null,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.zero,
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                  ),
                  child: Ink(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.black, width: 1),
                        gradient: LinearGradient(
                          colors: controlsEnabled  && needed  <= 0? [
                            Color.fromARGB(255, 76, 135, 175), // A green shade
                            Color.fromARGB(255, 46, 64, 125), // A darker green
                          ]: [
                            Colors.transparent, // A green shade
                            Colors.transparent , // A darker green
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(28),
                      ),
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                        constraints: BoxConstraints(minHeight: 40),
                        alignment: Alignment.center,
                        child: Stack(alignment: Alignment.center, children: [
                          Text(
                            'CHECK',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w900,
                              foreground: Paint()
                                ..style = PaintingStyle.stroke
                                ..strokeWidth = 4
                                ..color = Colors.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            'CHECK',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w900,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          )
                        ]),
                      ))),
              // On Fold
              ElevatedButton(
                  onPressed: controlsEnabled ? () => _onFold(seat) : null,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.zero,
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                  ),
                  child: Ink(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.black, width: 1),
                        gradient:controlsEnabled && isMyTurn
                            ? LinearGradient(
                                colors: [
                                  Color.fromARGB(
                                      255, 175, 76, 89), // A green shade
                                  Color.fromARGB(
                                      255, 125, 46, 57), // A darker green
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              )
                            : LinearGradient(
                                colors: [
                                   Colors.transparent, // A green shade
                            Colors.transparent ,// A darker green
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                        borderRadius: BorderRadius.circular(28),
                      ),
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                        constraints: BoxConstraints(minHeight: 40),
                        alignment: Alignment.center,
                        child: Stack(alignment: Alignment.center, children: [
                          Text(
                            'FOLD',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w900,
                              foreground: Paint()
                                ..style = PaintingStyle.stroke
                                ..strokeWidth = 4
                                ..color = Colors.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            'FOLD',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w900,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          )
                        ]),
                     
                      )
                      )
                      ),
              // On Call
              ElevatedButton(
                  onPressed: controlsEnabled
                      ? (hasActiveBet
                          ? (needed > 0 ? () => _onCall(seat) : null)
                          : (st >= _bigBlind
                              ? () => _onBetClicked(seat)
                              : () => _onBetAllIn(seat)))
                      : null,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.zero,
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                  ),
                  child: Ink(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.black, width: 1),
                        gradient: LinearGradient(
                          colors: controlsEnabled
                      ? (hasActiveBet
                          ? (needed > 0 ? [
                            Color.fromARGB(255, 173, 175, 76), // A green shade
                            Color.fromARGB(255, 108, 125, 46), // A darker green
                          ] :[
                            Colors.transparent, // A green shade
                            Colors.transparent , // A darker green
                          ])
                          : (st >= _bigBlind
                              ? [
                            Color.fromARGB(255, 173, 175, 76), // A green shade
                            Color.fromARGB(255, 108, 125, 46), // A darker green
                          ]
                              : []))
                      :[
                            Colors.transparent, // A green shade
                            Colors.transparent , // A darker green
                          ],
                          
                          
                          
                          //  controlsEnabled &&  needed > 0 
                          //     ? [
                          //   Color.fromARGB(255, 173, 175, 76), // A green shade
                          //   Color.fromARGB(255, 108, 125, 46), // A darker green
                          // ] : 
                          //[
                          //   Colors.transparent, // A green shade
                          //   Colors.transparent , // A darker green
                          // ]
                          
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(28),
                      ),
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                        constraints: BoxConstraints(minHeight: 40),
                        alignment: Alignment.center,
                        child: Stack(alignment: Alignment.center, children: [
                          Text(
                           controlsEnabled
                      ? (hasActiveBet
                          ? (needed > 0 ? 'CALL' : 'BET')
                          : (st >= _bigBlind
                              ? 'BET'
                              : 'CALL')
                              )
                      : 'BET',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w900,
                              foreground: Paint()
                                ..style = PaintingStyle.stroke
                                ..strokeWidth = 4
                                ..color = Colors.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            needed== 0? 'BET' :'CALL',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w900,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          )
                        ]),
                      ))),
              // On Raise
              ElevatedButton(
                onPressed: controlsEnabled ? () => _onRaiseClicked(seat) : null,
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.zero,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
                child: Ink(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black, width: 1),
                      gradient:LinearGradient(
                        colors:controlsEnabled ? [
                          Color(0xFF4CAF50), // A green shade
                          Color(0xFF2E7D32), // A darker green
                        ] : [
                            Colors.transparent, // A green shade
                            Colors.transparent , // A darker green
                          ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(28),
                    ),
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                      constraints: BoxConstraints(minHeight: 40),
                      alignment: Alignment.center,
                      child: Stack(alignment: Alignment.center, children: [
                        Text(
                          'RAISE',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w900,
                            foreground: Paint()
                              ..style = PaintingStyle.stroke
                              ..strokeWidth = 4
                              ..color = Colors.black,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        Text(
                          'RAISE',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        )
                      ]),
                    )),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildBetSliderPanel() {
    return Container(
      key: const ValueKey('betSliderPanel'),
      decoration: BoxDecoration(
        color: Colors.blueGrey.shade800,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white54),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              thumbColor: Colors.grey[400],
              activeTrackColor: Colors.grey[600],
              inactiveTrackColor: Colors.grey[400],
              overlayColor: Colors.grey.withOpacity(0.2),
              valueIndicatorColor: Colors.grey[600],
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6.0),
              trackHeight: 2.0,
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 10.0),
            ),
            child: Slider(
              value: _sliderValue,
              min: _sliderMin,
              max: _sliderMax,
              divisions: (_sliderMax - _sliderMin).toInt().clamp(1, 10000),
              label: _sliderValue.toStringAsFixed(0),
              onChanged: (val) {
                setState(() => _sliderValue = val);
              },
            ),
          ),
        ],
      ),
    );
  }

  void _onCancelBetOrRaise() {
    setState(() {
      _showSlider = false;
    });
  }

  void _onBetOrRaiseConfirmed(int seatIndex) {
    _turnTimerController.stop();
    setState(() {
      _showSlider = false;
    });

    if (_sliderIsRaise) {
      // For raises, we're already showing "Raise to" in the UI
      // We need to send this total amount to the server
      final totalAmount = _sliderValue.round();
      socket.emit("playerAction", {
        "auto": false,
        "roomId": _roomId,
        "seatIndex": seatIndex,
        "action": "raise",
        "amount": totalAmount,
      });
    } else {
      socket.emit("playerAction", {
        "auto": false,
        "roomId": _roomId,
        "seatIndex": seatIndex,
        "action": "bet",
        "amount": _sliderValue.round(),
      });
    }

    _printPotInfo();
  }

  void _onBetAllIn(int seatIndex) {
    _turnTimerController.stop();
    final st = _parseChips(players[seatIndex].chips);
    // Just emit a 'bet' with 'amount' == st:
    socket.emit("playerAction", {
      "auto": false,
      "roomId": _roomId,
      "seatIndex": seatIndex,
      "action": "bet",
      "amount": st,
    });
  }

  void _onBetClicked(int seatIndex) {
    final p = players[seatIndex];
    final st = _parseChips(p.chips).toDouble(); // total stack left
    _sliderIsRaise = false;

    // If the player has less than the big blind, then minBet = st (i.e. all-in).
    final double minBet = math.min(st, _bigBlind.toDouble());

    setState(() {
      _sliderMin = minBet;
      _sliderMax = st;
      _sliderValue = minBet;
      _showSlider = true;
    });
  }

  void _onRaiseClicked(int seatIndex) {
    final p = players[seatIndex];
    final st = _parseChips(p.chips).toDouble(); // Total stack left
    final alreadyContributed =
        p.contributedThisStreet.toDouble(); // What they've put in street

    // Calculate minimum raise (2x current bet or 2x big blind, whichever is higher)
    double minRaise = math.max(_currentBet * 2.0, _bigBlind * 2.0);

    // If they can't make the min raise, they can go all-in
    if (minRaise > (st + alreadyContributed)) {
      minRaise = st + alreadyContributed;
    }

    setState(() {
      _sliderIsRaise = true;
      _sliderMin = minRaise;
      _sliderMax =
          st + alreadyContributed; // Max includes what they already put in
      _sliderValue = _sliderMin;
      _showSlider = true;
    });
  }

  void _onCheck(int seatIndex) {
    _turnTimerController.stop();
    socket.emit("playerAction", {
      "auto": false,
      "roomId": _roomId,
      "seatIndex": seatIndex,
      "action": "check",
    });
  }

  void _onCall(int seatIndex) {
    _turnTimerController.stop();
    socket.emit("playerAction", {
      "auto": false,
      "roomId": _roomId,
      "seatIndex": seatIndex,
      "action": "call",
    });
  }

  void _onFold(int seatIndex) {
    _turnTimerController.stop();
    // Instead of local logic for folding:
    socket.emit("playerAction", {
      "auto": false,
      "roomId": _roomId,
      "seatIndex": seatIndex,
      "action": "fold",
    });
  }

  void _printPotInfo() {
    debugPrint("========== POT INFO ==========");
    debugPrint(
        "Current displayedPot = \$$_displayedPot, actualPot = \$$_actualPot");
    debugPrint("Pending bets: $_pendingBets");
    if (_sidePots.isEmpty) {
      debugPrint("No side pots. Pot = \$ $pot");
    } else {
      for (int i = 0; i < _sidePots.length; i++) {
        debugPrint("Pot ${i + 1}: \$${_sidePots[i].amount}");
      }
      debugPrint("Sum of side pots + main = \$ $pot");
    }
    debugPrint("=============================");
  }

  List<Widget> _buildPayoutAnimationWidgets() {
    if (gameStage != GameStage.payout || !_isAnimatingPayout) return [];
    final widgets = <Widget>[];
    for (final item in _currentPotChopItems) {
      final t = _payoutAnimationValue;
      final dx = item.start.dx + (item.end.dx - item.start.dx) * t;
      final dy = item.start.dy + (item.end.dy - item.start.dy) * t;

      widgets.add(
        Positioned(
          left: dx,
          top: dy,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5), // semi-opaque black
              border: Border.all(
                  color: Colors.grey.shade300,
                  width: 1), // light grey thin border
              borderRadius: BorderRadius.circular(20), // pill shape
            ),
            child: Text(
              '+ ${item.label}',
              style: const TextStyle(
                color: Colors.white, // white text on dark bg
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ),
      );
    }
    return widgets;
  }
}

// =========================
// Supporting enums & classes
// =========================

enum GameStage {
  preHand,
  flop,
  turn,
  river,
  showdown,
  payout,
}

enum FastForwardReason {
  none,
  allFolded,
  allAllIn,
}
